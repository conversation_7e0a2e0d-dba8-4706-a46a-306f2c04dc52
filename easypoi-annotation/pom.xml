<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<artifactId>easypoi-annotation</artifactId>
	<name>easypoi-annotation</name>
	<description>Easy POI注解模块</description>

	<build>
		<finalName>easypoi-annotation</finalName>
	</build>

	<parent>
		<groupId>com.sinoyd</groupId>
		<artifactId>easypoi</artifactId>
		<version>4.4.0-SNAPSHOT</version>
	</parent>

	<repositories>
		<repository>
			<id>maven-central</id>
			<name>maven-central</name>
			<url>http://nexusproxy.dev.yd/repository/maven-public/</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
	</repositories>
	<distributionManagement>
		<snapshotRepository>
			<id>nexus</id>
			<name>Nexus Snapshot</name>
			<url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
		</snapshotRepository>
		<site>
			<id>nexus</id>
			<name>Nexus Sites</name>
			<url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
		</site>
	</distributionManagement>
</project>
