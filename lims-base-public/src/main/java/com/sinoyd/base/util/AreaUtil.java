package com.sinoyd.base.util;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 区域工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/09
 **/
public class AreaUtil {

    /**
     * 获取区域名称
     *
     * @param areaId  区域ID
     * @param areaMap 区域Map
     * @return 区域名称
     */
    public static String getAreaName(String areaId, Map<String, DtoArea> areaMap) {
        DtoArea area = areaMap.getOrDefault(areaId, null);
        if (area == null) {
            return "";
        } else {
            List<String> areaNames = new ArrayList<>();
            areaNames.add(area.getAreaName());
            while (area != null && !UUIDHelper.guidEmpty().equals(area.getParentId()) && !StringUtils.isNull(area.getParentId()) && !"0".equals(area.getParentId())) {
                area = areaMap.getOrDefault(area.getParentId(), null);
                if (area != null) {
                    areaNames.add(area.getAreaName());
                }
            }
            Collections.reverse(areaNames);
            return String.join("/", areaNames);
        }
    }

    /**
     * 根据区域名称获取区域Id（支持层级匹配）
     *
     * @param areaList 区域数据集合
     * @param areaName 区域名称，支持格式：单个区域名称 或 "xx市/xx县/xx区" 层级格式
     * @return 区域id，如果未找到或输入为空则返回空字符串
     * @throws RuntimeException 如果找到多个匹配的区域
     */
    public static String getAreaId(List<DtoArea> areaList, String areaName) {
        // 如果区域名称为空，返回空字符串
        if (StringUtils.isEmpty(areaName)) {
            return "";
        }

        // 去除首尾空格
        areaName = areaName.trim();

        // 如果不包含分隔符，直接按单个区域名称处理
        if (!areaName.contains("/")) {
            return handleSingleAreaName(areaList, areaName);
        } else {
            return handleHierarchicalAreaName(areaList, areaName);
        }
    }

    /**
     * 处理单个区域名称
     *
     * @param areaList 区域数据集合
     * @param areaName 区域名称
     * @return 区域id，如果未找到则返回空字符串
     * @throws RuntimeException 如果找到多个匹配的区域
     */
    private static String handleSingleAreaName(List<DtoArea> areaList, String areaName) {
        List<DtoArea> matchedAreas = areaList.stream()
                .filter(area -> areaName.equals(area.getAreaName()))
                .collect(Collectors.toList());

        if (matchedAreas.size() > 1) {
            throw new RuntimeException("区域名称[" + areaName + "]不唯一");
        } else if (matchedAreas.isEmpty()) {
            return "";
        } else {
            return matchedAreas.get(0).getId();
        }
    }

    /**
     * 处理层级区域名称
     *
     * @param areaList 区域数据集合
     * @param areaName 区域名称
     * @return 区域id，如果未找到则返回空字符串
     */
    private static String handleHierarchicalAreaName(List<DtoArea> areaList, String areaName) {
        // 按"/"分割区域名称，从最后一级开始匹配
        String[] areaNames = areaName.split("/");
        String areaId = "";
        for (int i = 0; i < areaNames.length; i++) {
            String name = areaNames[i];
            List<DtoArea> currentList = areaList.stream()
                    .filter(area -> area.getAreaName().equals(name)).collect(Collectors.toList());
            if (currentList.size() > 1) {
                throw new RuntimeException("区域名称[" + areaName + "]不唯一");
            } else if (currentList.isEmpty()) {
                throw new RuntimeException("区域名称[" + areaName + "]在系统中不存在");
            } else {
                if (i == areaNames.length - 1) {
                    areaId = areaList.get(0).getId();
                } else {
                    List<DtoArea> list = new ArrayList<>();
                    for (DtoArea dtoArea : currentList) {
                        List<DtoArea> childList = findChildArea(areaList, dtoArea.getId());
                        if (!childList.isEmpty()) {
                            list.addAll(childList);
                        }
                    }
                    areaList = list;
                }
            }
        }
        return areaId;
    }

    //递归查询子级区域
    private static List<DtoArea> findChildArea(List<DtoArea> areaList, String areaId) {
        List<DtoArea> childAreas = areaList.stream().filter(a -> areaId.equals(a.getParentId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(childAreas)) {
            for (DtoArea area : childAreas) {
                childAreas.addAll(findChildArea(areaList, area.getId()));
            }
        }
        return childAreas;
    }
}
