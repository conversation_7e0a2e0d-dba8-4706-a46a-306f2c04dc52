package com.sinoyd.base.util;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/09
 **/
public class AreaUtil {

    /**
     * 获取区域名称
     *
     * @param areaId  区域ID
     * @param areaMap 区域Map
     * @return 区域名称
     */
    public static String getAreaName(String areaId, Map<String, DtoArea> areaMap) {
        DtoArea area = areaMap.getOrDefault(areaId, null);
        if (area == null) {
            return "";
        } else {
            List<String> areaNames = new ArrayList<>();
            areaNames.add(area.getAreaName());
            while (area != null && !UUIDHelper.guidEmpty().equals(area.getParentId()) && !StringUtils.isNull(area.getParentId()) && !"0".equals(area.getParentId())) {
                area = areaMap.getOrDefault(area.getParentId(), null);
                if (area != null) {
                    areaNames.add(area.getAreaName());
                }
            }
            Collections.reverse(areaNames);
            return String.join("/", areaNames);
        }
    }

    /**
     * 根据区域名称获取区域Id（支持层级匹配）
     *
     * @param areaList 区域数据集合
     * @param areaName 区域名称，支持格式：单个区域名称 或 "xx市/xx县/xx区" 层级格式
     * @return 区域id，如果未找到或输入为空则返回空字符串
     */
    public static String getAreaId(List<DtoArea> areaList, String areaName) {
        // 如果区域名称为空，返回空字符串
        if (StringUtils.isEmpty(areaName)) {
            return "";
        }

        // 去除首尾空格
        areaName = areaName.trim();

        // 如果不包含分隔符，直接按单个区域名称处理
        if (!areaName.contains("/")) {
            return handleSingleAreaName(areaList, areaName);
        }

        // 按"/"分割区域名称，从最后一级开始匹配
        String[] areaNames = areaName.split("/");

        // 去除每个区域名称的首尾空格
        for (int i = 0; i < areaNames.length; i++) {
            areaNames[i] = areaNames[i].trim();
        }

        // 从最后一级区域开始匹配
        return handleHierarchicalAreaName(areaList, areaNames);
    }

    /**
     * 处理单个区域名称匹配
     *
     * @param areaList 区域数据集合
     * @param areaName 单个区域名称
     * @return 区域id，如果未找到唯一匹配则返回空字符串
     */
    private static String handleSingleAreaName(List<DtoArea> areaList, String areaName) {
        List<DtoArea> candidates = areaList.stream()
                .filter(area -> areaName.equals(area.getAreaName()))
                .collect(Collectors.toList());

        // 如果只找到一个匹配结果，直接返回
        if (candidates.size() == 1) {
            return candidates.get(0).getId();
        }

        // 如果找到多个或没有找到，返回空字符串
        return "";
    }

    /**
     * 处理层级区域名称匹配
     *
     * @param areaList   区域数据集合
     * @param areaNames  区域名称数组，按层级顺序排列
     * @return 区域id，如果未找到唯一匹配则返回空字符串
     */
    private static String handleHierarchicalAreaName(List<DtoArea> areaList, String[] areaNames) {
        // 从最后一级区域名称开始匹配
        String targetAreaName = areaNames[areaNames.length - 1];

        // 查找所有匹配最后一级区域名称的区域
        List<DtoArea> candidates = areaList.stream()
                .filter(area -> targetAreaName.equals(area.getAreaName()))
                .collect(Collectors.toList());

        // 如果没有找到匹配的区域，返回空字符串
        if (candidates.isEmpty()) {
            return "";
        }

        // 如果只找到一个匹配结果，直接返回
        if (candidates.size() == 1) {
            return candidates.get(0).getId();
        }

        // 如果找到多个匹配结果，需要通过上级区域进行筛选
        return filterByParentAreas(areaList, candidates, areaNames, areaNames.length - 2);
    }

    /**
     * 通过父级区域递归筛选候选区域
     *
     * @param areaList       区域数据集合
     * @param candidates     候选区域列表
     * @param areaNames      区域名称数组
     * @param parentIndex    当前要匹配的父级区域名称索引
     * @return 区域id，如果未找到唯一匹配则返回空字符串
     */
    private static String filterByParentAreas(List<DtoArea> areaList, List<DtoArea> candidates,
                                            String[] areaNames, int parentIndex) {
        // 如果已经没有更多的父级区域名称可以匹配，或者候选区域为空，返回空字符串
        if (parentIndex < 0 || candidates.isEmpty()) {
            return "";
        }

        // 如果只剩一个候选区域，返回其ID
        if (candidates.size() == 1) {
            return candidates.get(0).getId();
        }

        // 获取当前要匹配的父级区域名称
        String parentAreaName = areaNames[parentIndex];

        // 创建区域ID到区域对象的映射，提高查找效率
        Map<String, DtoArea> areaMap = areaList.stream()
                .collect(Collectors.toMap(DtoArea::getId, area -> area));

        // 筛选出父级区域名称匹配的候选区域
        List<DtoArea> filteredCandidates = candidates.stream()
                .filter(area -> {
                    // 获取当前区域的父级区域
                    DtoArea parentArea = areaMap.get(area.getParentId());
                    // 检查父级区域是否存在且名称匹配
                    return parentArea != null && parentAreaName.equals(parentArea.getAreaName());
                })
                .collect(Collectors.toList());

        // 递归处理下一级父级区域
        return filterByParentAreas(areaList, filteredCandidates, areaNames, parentIndex - 1);
    }
}
