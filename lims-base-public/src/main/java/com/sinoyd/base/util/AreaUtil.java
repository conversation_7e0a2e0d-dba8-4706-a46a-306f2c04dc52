package com.sinoyd.base.util;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 区域工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/06/09
 **/
public class AreaUtil {

    /**
     * 获取区域名称
     *
     * @param areaId  区域ID
     * @param areaMap 区域Map
     * @return 区域名称
     */
    public static String getAreaName(String areaId, Map<String, DtoArea> areaMap) {
        DtoArea area = areaMap.getOrDefault(areaId, null);
        if (area == null) {
            return "";
        } else {
            List<String> areaNames = new ArrayList<>();
            areaNames.add(area.getAreaName());
            while (area != null && !UUIDHelper.guidEmpty().equals(area.getParentId()) && !StringUtils.isNull(area.getParentId()) && !"0".equals(area.getParentId())) {
                area = areaMap.getOrDefault(area.getParentId(), null);
                if (area != null) {
                    areaNames.add(area.getAreaName());
                }
            }
            Collections.reverse(areaNames);
            return String.join("/", areaNames);
        }
    }

    /**
     * 根据区域名称获取区域Id（支持层级匹配）
     *
     * @param areaList 区域数据集合
     * @param areaName 区域名称，支持格式：单个区域名称 或 "xx市/xx县/xx区" 层级格式
     * @return 区域id，如果未找到或输入为空则返回空字符串
     * @throws RuntimeException 如果找到多个匹配的区域
     */
    public static String getAreaId(List<DtoArea> areaList, String areaName) {
        // 如果区域名称为空，返回空字符串
        if (StringUtils.isEmpty(areaName)) {
            return "";
        }

        // 去除首尾空格
        areaName = areaName.trim();

        // 如果不包含分隔符，直接按单个区域名称处理
        if (!areaName.contains("/")) {
            return handleSingleAreaName(areaList, areaName);
        }

        // 按"/"分割区域名称，从最后一级开始匹配
        String[] areaNames = areaName.split("/");

        // 去除每个区域名称的首尾空格
        for (int i = 0; i < areaNames.length; i++) {
            areaNames[i] = areaNames[i].trim();
        }

        // 从最后一级区域开始匹配
        return handleHierarchicalAreaName(areaList, areaNames);
    }

    /**
     * 处理单个区域名称匹配
     *
     * @param areaList 区域数据集合
     * @param areaName 单个区域名称
     * @return 区域id，如果未找到则返回空字符串
     * @throws RuntimeException 如果找到多个匹配的区域
     */
    private static String handleSingleAreaName(List<DtoArea> areaList, String areaName) {
        List<DtoArea> candidates = areaList.stream()
                .filter(area -> areaName.equals(area.getAreaName()))
                .collect(Collectors.toList());

        // 如果只找到一个匹配结果，直接返回
        if (candidates.size() == 1) {
            return candidates.get(0).getId();
        }

        // 如果找到多个匹配结果，抛出异常
        if (candidates.size() > 1) {
            throw new RuntimeException(String.format("区域名称[%s]不唯一", areaName));
        }

        // 如果没有找到，返回空字符串
        return "";
    }

    /**
     * 处理层级区域名称匹配
     *
     * @param areaList  区域数据集合
     * @param areaNames 区域名称数组，按层级顺序排列
     * @return 区域id，如果未找到唯一匹配则返回空字符串
     */
    private static String handleHierarchicalAreaName(List<DtoArea> areaList, String[] areaNames) {
        // 从最后一级区域名称开始匹配
        String targetAreaName = areaNames[areaNames.length - 1];

        // 查找所有匹配最后一级区域名称的区域
        List<DtoArea> candidates = areaList.stream()
                .filter(area -> targetAreaName.equals(area.getAreaName()))
                .collect(Collectors.toList());

        // 如果没有找到匹配的区域，返回空字符串
        if (candidates.isEmpty()) {
            return "";
        }

        // 如果只找到一个匹配结果，验证其层级路径是否匹配
        if (candidates.size() == 1) {
            if (validateAreaPath(areaList, candidates.get(0), areaNames)) {
                return candidates.get(0).getId();
            } else {
                return "";
            }
        }

        // 如果找到多个匹配结果，需要通过完整路径进行筛选
        return filterByCompleteAreaPath(areaList, candidates, areaNames);
    }

    /**
     * 验证区域路径是否匹配给定的层级名称数组
     *
     * @param areaList  区域数据集合
     * @param area      要验证的区域
     * @param areaNames 期望的区域名称数组（从顶级到底级）
     * @return 如果路径匹配返回true，否则返回false
     */
    private static boolean validateAreaPath(List<DtoArea> areaList, DtoArea area, String[] areaNames) {
        // 创建区域ID到区域对象的映射，提高查找效率
        Map<String, DtoArea> areaMap = areaList.stream()
                .collect(Collectors.toMap(DtoArea::getId, area1 -> area1));

        // 构建从当前区域到根区域的完整路径
        List<String> actualPath = new ArrayList<>();
        DtoArea currentArea = area;

        while (currentArea != null) {
            actualPath.add(currentArea.getAreaName());
            // 检查是否到达根节点
            if (UUIDHelper.guidEmpty().equals(currentArea.getParentId()) ||
                    StringUtils.isNull(currentArea.getParentId()) ||
                    "0".equals(currentArea.getParentId())) {
                break;
            }
            currentArea = areaMap.get(currentArea.getParentId());
        }

        // 反转路径，使其从根到叶子的顺序
        Collections.reverse(actualPath);

        // 检查实际路径是否与期望路径匹配
        if (actualPath.size() != areaNames.length) {
            return false;
        }

        for (int i = 0; i < areaNames.length; i++) {
            if (!areaNames[i].equals(actualPath.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 通过完整区域路径筛选候选区域
     *
     * @param areaList   区域数据集合
     * @param candidates 候选区域列表
     * @param areaNames  区域名称数组（从顶级到底级）
     * @return 区域id，如果未找到则返回空字符串
     * @throws RuntimeException 如果找到多个匹配的区域
     */
    private static String filterByCompleteAreaPath(List<DtoArea> areaList, List<DtoArea> candidates, String[] areaNames) {
        // 筛选出路径完全匹配的候选区域
        List<DtoArea> matchedCandidates = candidates.stream()
                .filter(candidate -> validateAreaPath(areaList, candidate, areaNames))
                .collect(Collectors.toList());

        // 如果找到唯一匹配，返回其ID
        if (matchedCandidates.size() == 1) {
            return matchedCandidates.get(0).getId();
        }

        // 如果找到多个匹配，抛出异常
        if (matchedCandidates.size() > 1) {
            String areaPath = String.join("/", areaNames);
            throw new RuntimeException(String.format("区域名称[%s]不唯一", areaPath));
        }

        // 如果没有找到匹配，返回空字符串
        return "";
    }
}
