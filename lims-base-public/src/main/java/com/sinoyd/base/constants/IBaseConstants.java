package com.sinoyd.base.constants;

import java.util.regex.Pattern;

/**
 * Base模块常量
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2022-10-11
 */
public interface IBaseConstants {

    /**
     * 通用审核结果key
     */
    String APPROVAL_GUARD_KEY = "auditResult";

    /**
     * 通用提交是否需要审核key
     */
    String SUBMIT_GUARD_KEY = "isAudit";

    /**
     * 在线值集合
     */
    String ONLINE_DATA_LIST = "OnlineDataList";

    /**
     * 实验室集合
     */
    String LABORATORY_DATA_LIST = "laboratoryDataList";

    /**
     * 空值显示
     */
    interface EmptyValueDisplay {

        /**
         * 斜杠显示
         */
        String SLASH = "/";

        /**
         * 空值
         */
        String EMPTY = "";

        /**
         * 浮点型默认值: 0.0
         */
        Double DOUBLE_DEFAULT = 0.0;
    }

    /**
     * 分割符号
     */
    interface SplitSymbol {

        /**
         * 逗号
         */
        String COMMA = ",";

        /**
         * 顿号
         */
        String CAESURA_SIGN = "、";
    }

    /**
     * 执行方式
     */
    interface AdviceWay {
        /**
         * 之前执行
         */
        String BEFORE = "before";

        /**
         * 之后执行
         */
        String AFTER = "after";
    }

    /**
     * 缓存过期时间定义，单位秒
     */
    interface CacheExpireTime {
        /**
         * 通用缓存过期时间，特殊情况需要自行定义
         */
        long COMMON_EXPIRE_TIME = 300;

    }

    /**
     * 缓存名称定义
     */
    interface CacheName {
        /**
         * 字典缓存
         */
        String DICT = "lims:dict";

        /**
         * 部门
         */
        String DEPARTMENT = "lims:department";

        /**
         * 框架中相关
         */
        String FRAME = "lims:frame";
    }

    /**
     * 用户类型
     */
    interface OrgType {
        /**
         * 企业单位
         */
        String ENT = "ent";

        /**
         * 运维单位
         */
        String YW = "yw";

        /**
         * 政府单位
         */
        String GOV = "gov";

        /**
         * 系统单位
         */
        String SYSTEM = "system";
    }

    /**
     * 角色名字
     */
    interface RoleName {
        /**
         * 默认角色名称
         */
        String DEFAULT_ROLE_NAME = "通用角色";
    }

    /**
     * easyPoi常量
     */
    interface Poi {
        /**
         * 导出时sheet页对象中属性对应的导出配置名称
         */
        interface SheetModelKey {
            /**
             * 标题
             */
            String EXPORT_PARAMS = "title";

            /**
             * 导出数据class类型
             */
            String ENTITY_CLASS = "entity";

            /**
             * 数据
             */
            String DATA_LIST = "data";
        }
    }

    /**
     * 正则表达式定义
     */
    interface RegExpression {
        /**
         * 是否数值
         */
        Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");
    }

    /**
     * 属性相关常量
     */
    interface FieldConstant {
        /**
         * 复制忽略的字段
         */
        String[] IGNORE_FIELDS = {"id", "creator", "createDate", "modifier", "modifyDate"};
    }

}
