package com.sinoyd.base.util;

import com.sinoyd.frame.dto.DtoArea;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AreaUtil测试类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/20
 */
public class AreaUtilTest {

    private List<DtoArea> areaList;

    @BeforeEach
    public void setUp() {
        areaList = new ArrayList<>();
        
        // 创建测试数据：江苏省/徐州市/市辖区/鼓楼区
        DtoArea jiangsu = createArea("1", "江苏省", "0");
        DtoArea xuzhou = createArea("2", "徐州市", "1");
        DtoArea shixiaqu = createArea("3", "市辖区", "2");
        DtoArea gulou = createArea("4", "鼓楼区", "3");
        
        // 添加一些其他区域作为干扰项
        DtoArea beijing = createArea("5", "北京市", "0");
        DtoArea chaoyang = createArea("6", "朝阳区", "5");
        DtoArea anotherGulou = createArea("7", "鼓楼区", "5"); // 北京的鼓楼区
        
        areaList.add(jiangsu);
        areaList.add(xuzhou);
        areaList.add(shixiaqu);
        areaList.add(gulou);
        areaList.add(beijing);
        areaList.add(chaoyang);
        areaList.add(anotherGulou);
    }

    private DtoArea createArea(String id, String areaName, String parentId) {
        // 创建一个简单的DtoArea实现用于测试
        return new DtoArea() {
            private String areaId = id;
            private String name = areaName;
            private String parent = parentId;

            public String getId() {
                return areaId;
            }

            public String getAreaName() {
                return name;
            }

            public String getParentId() {
                return parent;
            }

            // 如果DtoArea有setter方法，也需要实现
            public void setId(String id) {
                this.areaId = id;
            }

            public void setAreaName(String areaName) {
                this.name = areaName;
            }

            public void setParentId(String parentId) {
                this.parent = parentId;
            }
        };
    }

    @Test
    public void testGetAreaIdWithHierarchicalPath() {
        // 测试完整层级路径匹配
        String result = AreaUtil.getAreaId(areaList, "江苏省/徐州市/市辖区/鼓楼区");
        assertEquals("4", result, "应该返回江苏省下的鼓楼区ID");
    }

    @Test
    public void testGetAreaIdWithSingleName() {
        // 测试单个区域名称匹配（应该返回空，因为有多个鼓楼区）
        String result = AreaUtil.getAreaId(areaList, "鼓楼区");
        assertEquals("", result, "有多个鼓楼区时应该返回空字符串");
    }

    @Test
    public void testGetAreaIdWithPartialPath() {
        // 测试部分路径匹配
        String result = AreaUtil.getAreaId(areaList, "徐州市/市辖区/鼓楼区");
        assertEquals("4", result, "部分路径也应该能正确匹配");
    }

    @Test
    public void testGetAreaIdWithNonExistentPath() {
        // 测试不存在的路径
        String result = AreaUtil.getAreaId(areaList, "江苏省/南京市/鼓楼区");
        assertEquals("", result, "不存在的路径应该返回空字符串");
    }

    @Test
    public void testGetAreaIdWithEmptyInput() {
        // 测试空输入
        String result = AreaUtil.getAreaId(areaList, "");
        assertEquals("", result, "空输入应该返回空字符串");
        
        result = AreaUtil.getAreaId(areaList, null);
        assertEquals("", result, "null输入应该返回空字符串");
    }

    @Test
    public void testGetAreaIdWithWhitespace() {
        // 测试包含空格的输入
        String result = AreaUtil.getAreaId(areaList, " 江苏省 / 徐州市 / 市辖区 / 鼓楼区 ");
        assertEquals("4", result, "包含空格的输入应该能正确处理");
    }
}
