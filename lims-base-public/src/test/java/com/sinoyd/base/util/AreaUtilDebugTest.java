package com.sinoyd.base.util;

import com.sinoyd.frame.dto.DtoArea;

import java.util.ArrayList;
import java.util.List;

/**
 * AreaUtil调试测试类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/20
 */
public class AreaUtilDebugTest {

    public static void main(String[] args) {
        // 创建测试数据
        List<DtoArea> areaList = createTestData();

        // 测试问题场景
        String testAreaName = "江苏省/徐州市/市辖区/鼓楼区";
        System.out.println("测试区域名称: " + testAreaName);
        System.out.println();

        try {
            // 先测试正常版本
            System.out.println("=== 测试正常版本 ===");
            String result = AreaUtil.getAreaId(areaList, testAreaName);
            System.out.println("正常版本结果: " + result);
            System.out.println();

            // 再测试调试版本
            System.out.println("=== 测试调试版本 ===");
            String debugResult = AreaUtil.getAreaIdWithDebug(areaList, testAreaName);
            System.out.println("调试版本结果: " + debugResult);
        } catch (Exception e) {
            System.out.println();
            System.out.println("发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static List<DtoArea> createTestData() {
        List<DtoArea> areaList = new ArrayList<>();
        
        // 创建测试数据：江苏省/徐州市/市辖区/鼓楼区
        areaList.add(createArea("1", "江苏省", "0"));
        areaList.add(createArea("2", "徐州市", "1"));
        areaList.add(createArea("3", "市辖区", "2"));
        areaList.add(createArea("4", "鼓楼区", "3"));
        
        // 添加一些其他区域作为干扰项
        areaList.add(createArea("5", "北京市", "0"));
        areaList.add(createArea("6", "朝阳区", "5"));
        areaList.add(createArea("7", "鼓楼区", "5")); // 北京的鼓楼区
        
        return areaList;
    }

    private static DtoArea createArea(String id, String areaName, String parentId) {
        return new DtoArea() {
            private String areaId = id;
            private String name = areaName;
            private String parent = parentId;
            
            @Override
            public String getId() {
                return areaId;
            }
            
            @Override
            public String getAreaName() {
                return name;
            }
            
            @Override
            public String getParentId() {
                return parent;
            }
            
            @Override
            public void setId(String id) {
                this.areaId = id;
            }
            
            @Override
            public void setAreaName(String areaName) {
                this.name = areaName;
            }
            
            @Override
            public void setParentId(String parentId) {
                this.parent = parentId;
            }
        };
    }
}
