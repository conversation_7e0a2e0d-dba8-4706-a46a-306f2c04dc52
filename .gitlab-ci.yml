workflow:
  rules:
    - if: $CI_COMMIT_TAG

variables:
  TARGET_NAME: "lims-rms-app"
  # 减少下载log输出，将maven缓存设置当当前目录下以便启用gitlab-ci的cache
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  MAVEN_REPO_USERNAME: $MVN_USER  # 在GitLab设置Variables
  MAVEN_REPO_PASSWORD: $MVN_PWD

default:
  tags:
    - dind
  artifacts:
    paths:
      - .ci_job_status
      - application.jar
    expire_in: 2 hour
    when: always

stages:
  - build     # 打包jar
  - package   # 打包docker 镜像
  - deploy   # 推送到服务器
  #- test # 接口自动测试，这里测试完成会有dingding推送
  - notify # 失败时的推送
  #- nextcloud_upload # 发布到nextcloud
  - manual_docker  # 手动发布到阿里云

build-jar:
  stage: build
  image:
    name: maven:3.8.4-openjdk-8
  cache:
    paths:
      - .m2/repository
  script:
    - cp m2-settings.xml /root/.m2/settings.xml
    - mvn $MAVEN_CLI_OPTS clean package -Dmaven.test.skip=true
    - cp target/*.jar application.jar
  after_script:
    - echo -n "编译JAR包" > .ci_job_status

build-image:
  stage: package
  services:
    - name: registry.dev.yd/sinoyd/registry/docker:stable-dind
      alias: docker
      command: [ "--registry-mirror", "https://dockerproxy.dev.yd" ]
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker login --username=$DOCKER_USER_JSYUANDA -p "$DOCKER_PWD_JSYUANDA" registry.cn-shanghai.aliyuncs.com
  script:
    - docker pull $CI_REGISTRY_IMAGE:latest || true
    - docker build --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG --tag $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker push $CI_REGISTRY_IMAGE:latest
  after_script:
    - echo -n "Docker发布" > .ci_job_status


deploy-prod:
  stage: deploy
  image:
    name: registry.dev.yd/sinoyd/registry/gitlab-dingtalk
    entrypoint: [ "/bin/sh", "-c" ]
  script:
#    - curl "http://webhook.test.yd/12.72/hooks/redeploy?service=lims-rms-app&tag=$CI_COMMIT_TAG"
    - curl "http://*************:39999/hooks/redeploy?service=lims60_lims-rms-app&tag=$CI_COMMIT_TAG"
  after_script:
    - echo -n "服务器部署" > .ci_job_status

#metersphere:
#  stage: test
#  image:
#    name: registry.dev.yd/sinoyd/registry/gitlab-dingtalk
##    entrypoint: [ "/app/runtest.sh" ]
#  variables:
#    PLUGIN_TOKEN: $DINGTALK_TOKEN
#    PLUGIN_TEST_ENDPOINT: $TEST_ENDPOINT
#    PLUGIN_TEST_BODY: $TEST_BODY
#    PLUGIN_DEBUG: 1
#    PLUGIN_TPL_SUCCESS_IMAGE: https://cdn.ydlims.com/img/fun-success01.gif
#  script:
#    - "/app/runtest.sh"
#  after_script:
#    - echo -n "接口测试" > .ci_job_status

notify-dingtalk-failure:
  stage: notify
  image:
    name: registry.dev.yd/sinoyd/registry/gitlab-dingtalk
    entrypoint: [ "/bin/sh", "-c" ]
  when: on_failure
  variables:
    PLUGIN_TOKEN: $DINGTALK_TOKEN
    PLUGIN_TPL_FAILURE_IMAGE: https://cdn.ydlims.com/img/deploy-failure.jpg
  script:
    - if [[ -f .ci_job_status ]]; then export PLUGIN_TPL_FAILURE_LABEL="$(cat .ci_job_status)执行失败"; fi
    - /app/push.sh

#upload-nextcloud:
#  stage: nextcloud_upload
#  image: registry.dev.yd/sinoyd/registry/gitlab-dingtalk
#  variables:
#    PLUGIN_TOKEN: $DINGTALK_TOKEN
#    PLUGIN_TPL_FAILURE_IMAGE: https://cdn.ydlims.com/img/deploy-success.jpg
#  script:
#    - curl -u "$NEXTCLOUD_USER:$NEXTCLOUD_PWD" -T application.jar -X PUT "http://nextcloud.dev.yd/remote.php/dav/files/$NEXTCLOUD_USER/04_LIMS/6.0/web/rms/lims-rms-app-$CI_COMMIT_TAG.jar"
#    - /app/push.sh -s -t lims-rms-app-$CI_COMMIT_TAG.jar -m "版本发布成功，下载地址：http://nextcloud.dev.yd/remote.php/dav/files/$NEXTCLOUD_USER/04_LIMS/6.0/web/rms"

docker-build:
  stage: manual_docker
  services:
    - name: registry.dev.yd/sinoyd/registry/docker:stable-dind
      alias: docker
      command: [ "--registry-mirror", "https://dockerproxy.dev.yd" ]
  rules:
    - when: manual
  before_script:
    - docker login --username=$DOCKER_USER_JSYUANDA -p "$DOCKER_PWD_JSYUANDA" registry.cn-shanghai.aliyuncs.com
  script:
    - docker build --tag registry.cn-shanghai.aliyuncs.com/jsyuanda/lims-rms-app:$CI_COMMIT_TAG .
    - docker push registry.cn-shanghai.aliyuncs.com/jsyuanda/lims-rms-app:$CI_COMMIT_TAG