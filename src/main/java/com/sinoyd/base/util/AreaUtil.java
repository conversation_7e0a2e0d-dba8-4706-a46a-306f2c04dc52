    //递归查询子级区域
    private static List<DtoArea> findChildArea(List<DtoArea> areaList, String areaId) {
        List<DtoArea> childAreas = areaList.stream().filter(a -> areaId.equals(a.getParentId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(childAreas)) {
            List<DtoArea> newChildAreas = new ArrayList<>();
            for (DtoArea area : childAreas) {
                newChildAreas.addAll(findChildArea(areaList, area.getId()));
            }
            childAreas.addAll(newChildAreas);
        }
        return childAreas;
    }