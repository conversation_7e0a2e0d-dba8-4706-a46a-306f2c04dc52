package com.sinoyd.common.preview;

import com.sinoyd.common.utils.AsposeLicenseUtil;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;

/**
 * 文件预览器接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/5/19
 */
public interface IDocumentPreviewer {

    /**
     * 验证Aspose证书(word和excel)
     */
    default void verifyLicense() {
        if (!AsposeLicenseUtil.isLicense()) {
            throw new RuntimeException("验证远大文件体系证书失败");
        }
    }

    /**
     * 验证Aspose证书(pdf)
     */
    default void verifyPDFLicense() {
        if (!AsposeLicenseUtil.isPdfLicense()) {
            throw new RuntimeException("验证远大文件体系PDF证书失败");
        }
    }

    /**
     * 后置处理
     *
     * @param vo       文档预览VO实例
     * @param response 输出流
     */
    default void postProcessing(DocumentPreviewVO vo, HttpServletResponse response) {
        if (vo.getIsAddWaterMark()) {
            FileUtil.watermark(vo.getPreviewFileFullPath(), vo.getStyleVO());
        }
        if(vo.getInputStream()!=null){
            FileUtil.statusPic(vo.getPreviewFileFullPath(), vo.getInputStream());
        }
        //下载生成的临时预览文件
        FileUtil.download(vo.getPreviewFileFullPath(), vo.getPreviewFileName(), response);
        //删除生成的临时预览文件
        FileUtil.delete(vo.getPreviewFileFullPath());
    }

    /**
     * 文档预览
     *
     * @param rootPath 文档根目录
     * @param vo       文档预览VO实例
     * @param response 输出流
     */
    void previewAsPDF(String rootPath, DocumentPreviewVO vo, HttpServletResponse response);

    /**
     * 文档预览，流的形式，将文档转换成html放入响应流给前端
     *
     * @param os       字节数组输出流
     * @param response 响应流
     */
    void previewAsHtml(ByteArrayOutputStream os, HttpServletResponse response);

    /**
     * 加载预览文件的信息(文件名和全路径)
     *
     * @param rootPath 根路径
     * @param vo       文档预览VO实例
     */
    default void loadPreviewFileInfo(String rootPath, DocumentPreviewVO vo) {
        //获取子路径
        String subPath = vo.getSourceFilePath().substring(0, vo.getSourceFilePath().lastIndexOf("/"));
        //预览临时文件名
        String tempFileName = vo.getSourceFileName().substring(0, vo.getSourceFileName().lastIndexOf(".")) + ".pdf";
        //预览临时文件全路径
        String tempFileFullPath = rootPath + File.separator + subPath + File.separator
                + FileUtil.loadFileNameWithTimestamp(tempFileName);
        vo.setPreviewFileName(tempFileName).setPreviewFileFullPath(tempFileFullPath);
    }

}