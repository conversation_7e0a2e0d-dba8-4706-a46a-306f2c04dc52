package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.rms.dto.DtoTest;
import com.sinoyd.lims.rms.feign.ITestFeignService;
import com.sinoyd.lims.rms.service.ITestClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 测试项目对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/1
 */
@Service
public class TestClientServiceImpl implements ITestClientService {

    private ITestFeignService testFeignService;

    @Override
    public List<DtoTest> findAllByIds(Collection<String> ids) {
        RestResponse<List<DtoTest>> restResponse = testFeignService.findAllByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public Map<String, DtoTest> findTestMapByIds(Collection<String> ids) {
        List<DtoTest> dataList = findAllByIds(ids);
        return dataList.stream().collect(java.util.stream.Collectors.toMap(DtoTest::getId, p -> p));
    }

    @Override
    public Map<String, String> findMapByIds(Collection<String> ids) {
        List<DtoTest> dataList = findAllByIds(ids);
        return dataList.stream().collect(java.util.stream.Collectors.toMap(DtoTest::getId, DtoTest::getAnalyzeItemName));
    }

    @Override
    public DtoTest findOne(String id) {
        RestResponse<DtoTest> restResponse = testFeignService.findOne(id);
        if (restResponse.isSuccess()) return restResponse.getData();
        throw new BaseException("获取测试项目详情发生错误...");
    }

    @Override
    public List<DtoTest> findBySampleTypeId(String sampleTypeId) {
        RestResponse<List<DtoTest>> restResponse = testFeignService.findBySampleTypeId(sampleTypeId);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoTest> findValidList() {
        RestResponse<List<DtoTest>> restResponse = testFeignService.findValidList();
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    @Async
    public void updateUsageNum(List<String> ids) {
        RestResponse<List<DtoTest>> restResponse = testFeignService.updateUsageNum(ids);
        if (!restResponse.isSuccess()) {
            throw new BaseException("更新测试项目使用次数发生错误...");
        }
    }

    @Autowired
    @Lazy
    public void setTestFeignService(ITestFeignService testFeignService) {
        this.testFeignService = testFeignService;
    }
}