package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.criteria.ReagentConfigCriteria;
import com.sinoyd.lims.rms.dto.DtoReagentConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/reagentConfig",
        configuration = {FeignConfig.class}
)
public interface IReagentConfigFeignService {

    /**
     * 分页查询试剂记录配置数据
     *
     * @param page     当前页数
     * @param rows     每页条数
     * @param sort     排序
     * @param criteria 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    RestResponse<List<DtoReagentConfig>> findPostPage(@RequestParam("page") Integer page,
                                                    @RequestParam("rows") Integer rows,
                                                    @RequestParam("sort") String sort,
                                                    @RequestBody ReagentConfigCriteria criteria);

}
