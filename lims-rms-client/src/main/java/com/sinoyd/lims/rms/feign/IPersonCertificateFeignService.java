package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoPersonCertificate;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 人员证书Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/18
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/personCertificate",
        configuration = {FeignConfig.class}
)
public interface IPersonCertificateFeignService {

    /**
     * 根据人员id集合查询证书数据
     *
     * @param personIds 人员id集合
     * @return 证书数据
     */
    @PostMapping("/person/ids")
    RestResponse<List<DtoPersonCertificate>> findByPersonIds(@RequestBody Collection<String> personIds);
}
