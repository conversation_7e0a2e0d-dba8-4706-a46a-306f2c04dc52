package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoPerson;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 人员对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
public interface IPersonClientService {

    /**
     * 根据id查询
     *
     * @param ids 人员id集合
     * @return 人员集合
     */
    List<DtoPerson> findAllByIds(Collection<String> ids);

    /**
     * 根据id查询，并将id和实体组装成Map形式
     *
     * @param ids 人员id集合
     * @return Map<人员id ， 人员名称>
     */
    Map<String, String> findMapByIds(Collection<String> ids);

    /**
     * 根据id查询人员与签名路径映射Map
     *
     * @param ids 人员id集合
     * @return 响应结果
     */
    Map<String, String> findSignMapByIds(Collection<String> ids);

    /**
     * 根据岗位id集合查询人员数据
     *
     * @param positionIds 岗位id集合
     * @return 人员数据
     */
    List<DtoPerson> findByPositionIds(Collection<String> positionIds);

    /**
     * 查询所有人员
     *
     * @return 人员数据
     */
    List<DtoPerson> findAll();
}
