package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoPersonCertificate;

import java.util.Collection;
import java.util.List;

/**
 * 人员证书对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/18
 */
public interface IPersonCertificateClientService {

    /**
     * 根据人员id集合查询证书数据
     *
     * @param personIds 人员id集合
     * @return 证书数据
     */
    List<DtoPersonCertificate> findByPersonIds(Collection<String> personIds);
}
