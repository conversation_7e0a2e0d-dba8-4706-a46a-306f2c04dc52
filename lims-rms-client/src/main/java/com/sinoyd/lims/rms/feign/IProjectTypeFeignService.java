package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoProjectFlowProperty;
import com.sinoyd.lims.rms.dto.DtoProjectType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 项目类型Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/projectType",
        configuration = {FeignConfig.class}
)
public interface IProjectTypeFeignService {

    /**
     * 根据项目类型id查询
     *
     * @param ids 项目类型id集合
     * @return 响应结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoProjectType>> findAllByIds(@RequestBody Collection<String> ids);

    /**
     * 根据项目类型id查询
     *
     * @param id 项目类型id
     * @return 响应结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoProjectType> findOne(@PathVariable("id") String id);

    /**
     * 查询所有的项目类型
     *
     * @return 响应结果
     */
    @GetMapping
    RestResponse<List<DtoProjectType>> findAll();

    /**
     * 加载项目类型下的流程性质
     *
     * @param projectTypeId 项目类型id
     * @return 响应结果
     */
    @GetMapping("/property/{projectTypeId}")
    RestResponse<List<DtoProjectFlowProperty>> loadFlowProperty(@PathVariable("projectTypeId") String projectTypeId);
}
