package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 编号生成Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/10
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/serialNo",
        configuration = {FeignConfig.class}
)
public interface ISerialNoFeignService {

    /**
     * 产生序列号
     *
     * @param params                   数据参数
     * @param serialIdentifierConfigId 编号配置实体id
     * @return 响应结果
     */
    @PostMapping("/{serialIdentifierConfigId}")
    RestResponse<String> generateSerialNo(@RequestBody Map<String, Object> params,
                                          @PathVariable("serialIdentifierConfigId") String serialIdentifierConfigId);
}
