package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 人员证书对外实体
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/05/18
 */
@Data
public class DtoPersonCertificate {

    /**
     * 主键id
     */
    private String id;

    /**
     * 人员Id（Guid）
     */
    private String personId;

    /**
     * 证书名称
     */
    private String name;

    /**
     * 证书编号
     */
    private String certificationNo;

    /**
     * 发证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate;

    /**
     * 配置类型，常量lims_rms_certType编码，多选用英文逗号间隔
     */
    private String certificationTypeCode;


    /**
     * 发证机关
     */
    private String issuedOrg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 持有人员姓名
     */
    private String personName;

    /**
     * 持有人员用户编号
     */
    private String personNo;

    /**
     * 状态名称
     */
    private String statusName;
}
