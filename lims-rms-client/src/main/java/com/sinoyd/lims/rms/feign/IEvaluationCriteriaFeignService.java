package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoEvaluationCriteria;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 评价标准 Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/evaluationCriteria",
        configuration = {FeignConfig.class}
)
public interface IEvaluationCriteriaFeignService {
    /**
     * 根据标识集合查询
     *
     * @param ids id集合
     * @return 结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoEvaluationCriteria>> findByIds(@RequestBody Collection<String> ids);
}