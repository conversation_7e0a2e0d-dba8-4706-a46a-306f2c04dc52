package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoEvaluationLevel;

import java.util.Collection;
import java.util.List;

/**
 * 评价等级 对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
public interface IEvaluationLevelClientService {
    /**
     * 根据标识集合查询
     *
     * @param ids 标识集合
     * @return 结果
     */
    List<DtoEvaluationLevel> findByIds(Collection<String> ids);
}
