package com.sinoyd.lims.rms.service;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 检测类型对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/4
 */
public interface ISampleTypeClientService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 检测类型集合
     */
    List<DtoSampleType> findAllByIds(Collection<String> ids);

    /**
     * 根据id集合查询，并把返回结果映射成Map格式
     *
     * @param ids id集合
     * @return Map<检测类型id, 检测类型名称>
     */
    Map<String, String> findMapByIds(Collection<String> ids);

    /**
     * 根据检测类型id查询
     *
     * @param sampleTypeId 检测类型id
     * @return 检测类型实体
     */
    DtoSampleType findOne(String sampleTypeId);


    /**
     * 根据行业类型查询检测类型（大类）
     *
     * @return 检测类型集合
     */
    List<DtoSampleType> findAllBigSampleType();

}