package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoAnalyzeItemSort;
import com.sinoyd.lims.rms.feign.IAnalyzeItemSortFeignService;
import com.sinoyd.lims.rms.service.IAnalyzeItemSortClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 分析项目排序对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/26
 */
@Service
public class AnalyzeItemSortClientServiceImpl implements IAnalyzeItemSortClientService {

    private IAnalyzeItemSortFeignService analyzeItemSortFeignService;

    @Override
    public DtoAnalyzeItemSort find(String id) {
        RestResponse<DtoAnalyzeItemSort> response = analyzeItemSortFeignService.find(id);
        return response.isSuccess() ? response.getData() : null;
    }

    @Override
    public List<DtoAnalyzeItemSort> findByIds(Collection<String> ids) {
        RestResponse<List<DtoAnalyzeItemSort>> response = analyzeItemSortFeignService.findByIds(ids);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Autowired
    public void setAnalyzeItemSortFeignService(IAnalyzeItemSortFeignService analyzeItemSortFeignService) {
        this.analyzeItemSortFeignService = analyzeItemSortFeignService;
    }
}
