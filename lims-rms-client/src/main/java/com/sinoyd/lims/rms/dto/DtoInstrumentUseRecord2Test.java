package com.sinoyd.lims.rms.dto;

import lombok.Data;

/**
 * 仪器使用记录关联测试项目对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/15
 */
@Data
public class DtoInstrumentUseRecord2Test {

    /**
     * 主键
     */
    private String id;

    /**
     * 仪器使用记录id
     */
    private String instrumentUseRecordId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 方法标准号
     */
    private String methodStandardNo;

    /**
     * 测试项目父id
     */
    private String parentId;
}
