package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoEnterpriseMonitorPoint;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 企业点位feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/11
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/enterpriseMonitorPoint",
        configuration = {FeignConfig.class}
)
public interface IEnterpriseMonitorPointFeignService {

    /**
     * 跟模id集合查询
     *
     * @param ids id集合
     * @return 响应结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoEnterpriseMonitorPoint>> findByIds(@RequestBody Collection<String> ids);
}
