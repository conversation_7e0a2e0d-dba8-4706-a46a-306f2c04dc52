package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 检测类型Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/4
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/sampleType",
        configuration = {FeignConfig.class}
)
public interface ISampleTypeFeignService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 响应结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoSampleType>> findAllByIds(@RequestBody Collection<String> ids);

    /**
     * 根据检测类型id查询
     *
     * @param id 检测类型id
     * @return 响应结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoSampleType> findOne(@PathVariable(name = "id") String id);


    /**
     * 根据行业类型查询检测类型（大类）
     *
     * @return 检测类型集合
     */
    @GetMapping("/big/all")
    RestResponse<List<DtoSampleType>> findAllBigSampleType();

}
