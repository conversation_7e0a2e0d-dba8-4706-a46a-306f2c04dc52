package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.criteria.InstrumentOutDeviceCriteria;
import com.sinoyd.lims.rms.dto.DtoInstrumentOutDevice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 仪器出入库设备feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/18
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/instrument/device",
        configuration = {FeignConfig.class}
)
public interface IInstrumentOutDeviceFeignService {


    /**
     * 分页查询（用于仪器出库确认列表）
     *
     * @param criteria 查询条件
     * @return 结果
     */
    @PostMapping("/page")
    RestResponse<List<DtoInstrumentOutDevice>> findFeignPage(@RequestParam("page") Integer page,
                                                                    @RequestParam("rows") Integer rows,
                                                                    @RequestParam("sort") String sort,
                                                                    @RequestBody InstrumentOutDeviceCriteria criteria);
}
