package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoTestPositionPerson;

import java.util.Collection;
import java.util.List;

/**
 * 测试岗位关联人员对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/24
 */
public interface ITestPositionPersonClientService {

    /**
     * 根据人员id集合查询岗位关联数据
     *
     * @param personIds 人员id集合
     * @return 岗位关联数据
     */
    List<DtoTestPositionPerson> queryByPersonIds(Collection<String> personIds);
}
