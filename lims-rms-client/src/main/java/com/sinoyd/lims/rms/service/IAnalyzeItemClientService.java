package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoAnalyzeItem;
import com.sinoyd.lims.rms.vo.AnalyzeSortQueryParamVO;

import java.util.Collection;
import java.util.List;

/**
 * 分析项目对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/12
 */
public interface IAnalyzeItemClientService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 分析项目数据
     */
    List<DtoAnalyzeItem> findByIds(Collection<String> ids);

    /**
     * 按主键以及分析项目排序id查询排序后的数据
     *
     * @param paramVO 查询传参
     * @return 结果
     */
    List<DtoAnalyzeItem> findSortList(AnalyzeSortQueryParamVO paramVO);
}
