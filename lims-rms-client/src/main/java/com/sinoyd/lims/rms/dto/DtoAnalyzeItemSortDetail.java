package com.sinoyd.lims.rms.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Transient;

/**
 * 分析项目排序详细对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/26
 */
@Data
public class DtoAnalyzeItemSortDetail {

    /**
     * 主键id
     */
    private String id;

    /**
     * 分析项目排序Id
     */
    private String sortId;

    /**
     * 分析项目Id
     */
    private String analyzeItemId;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;
}
