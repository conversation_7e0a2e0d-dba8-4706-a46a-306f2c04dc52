package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoCarManage;
import com.sinoyd.lims.rms.feign.ICarManageFeignService;
import com.sinoyd.lims.rms.service.ICarManageClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 车辆管理对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/23
 */
@Service
public class CarManageClientServiceImpl implements ICarManageClientService {

    private ICarManageFeignService carManageFeignService;

    @Override
    public DtoCarManage findOne(String id) {
        RestResponse<DtoCarManage> response = carManageFeignService.findOne(id);
        if (response.isSuccess()) {
            return response.getData();
        }
        return null;
    }

    @Autowired
    public void setCarManageFeignService(ICarManageFeignService carManageFeignService) {
        this.carManageFeignService = carManageFeignService;
    }
}
