package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.Date;

/**
 * 监测计划点位和测试项目关联对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/21
 */
@Data
public class DtoMonitorPlanPoint2Test {

    /**
     * 主键
     */
    private String id;

    /**
     * 监测计划点位关联id
     */
    private String plan2PointId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 是否现场
     */
    private Boolean isCompleteField;

    /**
     * 分包类型，枚举管理EnumSubcontractType
     */
    private Integer subcontractType;

    /**
     * 批次
     */
    private Integer batchTimes;

    /**
     * 样次
     */
    private Integer sampleTimes;

    /**
     * 评价标准id
     */
    private String evaluationCriteriaId;

    /**
     * 评价等级id
     */
    private String evaluationLevelId;

    /**
     * 评价限值id
     */
    private String evaluationValueId;

    /**
     * 上限值
     */
    private String upperLimit;

    /**
     * 上限符号
     */
    private String upperSymbol;

    /**
     * 下限值
     */
    private String lowerLimit;

    /**
     * 下限符号
     */
    private String lowerSymbol;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 假删标识
     */
    private Boolean isDeleted;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date modifyDate;

    /**
     * 分析因子id
     */
    private String analyzeItemId;

    /**
     * 分析因子名称
     */
    private String analyzeItemName;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法编号
     */
    private String methodStandard;

    /**
     * 评价标准名称
     */
    private String evaluateCriteriaName;

    /**
     * 评价标准等级名称
     */
    private String evaluateLevelName;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 该测试项目的监测类型大类id
     */
    private String sampleTypeId;
}