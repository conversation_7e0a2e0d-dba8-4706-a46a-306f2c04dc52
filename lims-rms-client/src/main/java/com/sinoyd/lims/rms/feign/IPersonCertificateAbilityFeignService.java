package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoPersonCertificateAbility;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 人员证书检测能力Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/18
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/personCertificateAbility",
        configuration = {FeignConfig.class}
)
public interface IPersonCertificateAbilityFeignService {


    /**
     * 根据证书id集合查询检测能力数据
     *
     * @param certIds 证书id集合
     * @return 检测能力
     */
    @PostMapping("/cert/ids")
    RestResponse<List<DtoPersonCertificateAbility>> findByCertIds(@RequestBody Collection<String> certIds);
}
