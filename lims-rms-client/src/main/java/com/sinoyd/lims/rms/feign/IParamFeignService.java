package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 参数Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/param",
        configuration = {FeignConfig.class}
)
public interface IParamFeignService {

    /**
     * 保存参数
     *
     * @param param 参数实体
     * @return RestResponse<DtoParam>
     */
    @PostMapping
    RestResponse<DtoParam> save(@RequestBody DtoParam param);

    /**
     * 根据参数名称查询参数
     *
     * @param paramName 参数名称
     * @return RestResponse<DtoParam>
     */
    @GetMapping("/findByName")
    RestResponse<DtoParam> findByName(@RequestParam String paramName);

}
