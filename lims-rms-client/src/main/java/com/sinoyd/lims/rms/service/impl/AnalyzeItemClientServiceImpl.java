package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoAnalyzeItem;
import com.sinoyd.lims.rms.feign.IAnalyzeItemFeignService;
import com.sinoyd.lims.rms.service.IAnalyzeItemClientService;
import com.sinoyd.lims.rms.vo.AnalyzeSortQueryParamVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 分析项目对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/12
 */
@Service
public class AnalyzeItemClientServiceImpl implements IAnalyzeItemClientService {

    private IAnalyzeItemFeignService analyzeItemFeignService;

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 分析项目数据
     */
    @Override
    public List<DtoAnalyzeItem> findByIds(Collection<String> ids) {
        RestResponse<List<DtoAnalyzeItem>> response = analyzeItemFeignService.findByIds(ids);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoAnalyzeItem> findSortList(AnalyzeSortQueryParamVO paramVO) {
        RestResponse<List<DtoAnalyzeItem>> response = analyzeItemFeignService.findSortList(paramVO);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Autowired
    @Lazy
    public void setAnalyzeItemFeignService(IAnalyzeItemFeignService analyzeItemFeignService) {
        this.analyzeItemFeignService = analyzeItemFeignService;
    }
}
