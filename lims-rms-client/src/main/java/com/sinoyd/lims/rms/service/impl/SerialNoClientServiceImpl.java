package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.lims.rms.feign.ISerialNoFeignService;
import com.sinoyd.lims.rms.service.ISerialNoClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 序列号生成对外实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/10
 */
@Service
public class SerialNoClientServiceImpl implements ISerialNoClientService {

    private ISerialNoFeignService serialNoFeignService;

    @Override
    public String generateSerialNo(Map<String, Object> params, String serialIdentifierConfigId) {
        if(PrincipalContextUser.getPrincipal() == null){
            throw new RuntimeException("用户信息不存在");
        }
        params.put("currentPrincipal", SecurityContextHolder.getContext().getAuthentication().getPrincipal());
        RestResponse<String> restResponse = serialNoFeignService.generateSerialNo(params, serialIdentifierConfigId);
        if (restResponse.isSuccess()) return restResponse.getData();
        throw new BaseException("生成序列号失败");
    }

    @Autowired
    public void setSerialNoFeignService(ISerialNoFeignService serialNoFeignService) {
        this.serialNoFeignService = serialNoFeignService;
    }
}