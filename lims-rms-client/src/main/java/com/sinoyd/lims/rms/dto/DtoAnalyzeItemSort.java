package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.List;

/**
 * 分析项目排序对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/26
 */
@Data
public class DtoAnalyzeItemSort {

    /**
     * 主键id
     */
    private String id;

    /**
     * 排序名称
     */
    private String sortName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序详情
     */
    private List<DtoAnalyzeItemSortDetail> analyzeItemSortDetailList;
}
