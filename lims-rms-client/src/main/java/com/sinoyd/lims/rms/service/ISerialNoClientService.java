package com.sinoyd.lims.rms.service;

import java.util.Map;

/**
 * 序列号生成对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/10
 */
public interface ISerialNoClientService {

    /**
     * 产生序列号
     *
     * @param params                   数据参数
     * @param serialIdentifierConfigId 编号配置实体id
     * @return 响应结果
     */
    String generateSerialNo(Map<String, Object> params,
                            String serialIdentifierConfigId);
}