package com.sinoyd.lims.rms.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 企业点位测试项目对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/11
 */
@Data
@Accessors(chain = true)
public class DtoEnterpriseMonitorPointTest {

    /**
     * 主键
     */
    private String id;

    /**
     * 企业点位id
     */
    private String enterprisePointId;

    /**
     * 测试项目id
     */
    private String testId;

    /**
     * 分包类型，枚举管理EnumSubcontractType
     */
    private Integer subcontractType;

    /**
     * 是否现场数据
     */
    private Boolean isCompleteField = false;

    /**
     * 批次
     */
    private Integer batchTimes;

    /**
     * 样次
     */
    private Integer sampleTimes;

    /**
     * 评价标准id
     */
    private String evaluationCriteriaId;

    /**
     * 评价等级id
     */
    private String evaluationLevelId;

    /**
     * 上限值
     */
    private String upperLimit;

    /**
     * 上限符号
     */
    private String upperSymbol;

    /**
     * 下限值
     */
    private String lowerLimit;

    /**
     * 下限符号
     */
    private String lowerSymbol;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 分析因子id
     */
    private String analyzeItemId;

    /**
     * 分析因子名称
     */
    private String analyzeItemName;

    /**
     * 分析方法id
     */
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法编号
     */
    private String methodStandard;

    /**
     * 评价标准名称
     */
    private String evaluateCriteriaName;

    /**
     * 评价标准等级名称
     */
    private String evaluateLevelName;

    /**
     * 量纲名称
     */
    private String dimensionName;

    /**
     * 该测试项目的监测类型大类id
     */
    private String sampleTypeId;

    /**
     * 总称测试项目子项
     */
    private List<DtoEnterpriseMonitorPointTest> children;

    /**
     * 父测试项目标识
     */
    private String parentTestId;

    /**
     * 评价限值id
     */
    private String evaluationValueId;
}