package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.Date;

/**
 * 评价标准
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@Data
public class DtoEvaluationCriteria {
    /**
     * 主键id
     */
    private String id;

    /**
     * 评价标准名称
     */
    private String name;

    /**
     * 标准代码
     */
    private String code;

    /**
     * 标准类型，常量limg_lim_evaluationType编码
     */
    private String categoryCode;

    /**
     * 检测类型
     */
    private String sampleTypeId;

    /**
     * 实施时间
     */
    private Date startTime;

    /**
     * 废止时间
     */
    private Date endTime;

    /**
     * 标准状态，枚举EnumEvaluateCriteriaStatus值
     */
    private Integer status;

    /**
     * 适用范围
     */
    private String applyScope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
