package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoInstrument;

import java.util.Collection;
import java.util.List;

/**
 * 仪器对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/11
 */
public interface IInstrumentClientService {

    /**
     * 根据仪器id查询仪器数据
     *
     * @param ids 仪器id集合
     * @return 仪器数据
     */
    List<DtoInstrument> findByIds(Collection<String> ids);
}
