package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoEnterprise;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 企业Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/enterprise",
        configuration = {FeignConfig.class}
)
public interface IEnterpriseFeignService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 企业实体集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoEnterprise>> findAllByIds(@RequestBody Collection<String> ids);
}
