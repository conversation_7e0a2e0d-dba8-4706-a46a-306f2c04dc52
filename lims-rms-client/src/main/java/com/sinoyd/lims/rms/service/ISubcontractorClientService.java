package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoSubcontractor;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 分包商对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/23
 */
public interface ISubcontractorClientService {

    /**
     * 根据给定的id集合查找分包商信息列表
     *
     * @param ids 分包商id集合
     * @return 返回包含查找到的分包商信息的DtoSubcontractor对象列表
     */
    List<DtoSubcontractor> findByIds(Collection<String> ids);

    /**
     * 根据id集合查询，并把返回结果映射成Map格式
     *
     * @param ids id集合
     * @return Map<分包商id, 分包商名称>
     */
    Map<String, String> findMapByIds(Collection<String> ids);

}
