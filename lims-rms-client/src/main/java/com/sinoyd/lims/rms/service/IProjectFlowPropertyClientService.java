package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoProjectFlowProperty;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 流程性质对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
public interface IProjectFlowPropertyClientService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 流程性质集合
     */
    List<DtoProjectFlowProperty> findAllByIds(Collection<String> ids);

    /**
     * 根据id集合查询，并把返回结果映射成Map格式
     *
     * @param ids id集合
     * @return Map<项目类型id, 流程性质名称>
     */
    Map<String, String> findMapByIds(Collection<String> ids);

    /**
     * 根据流程标志查询流程性质数据
     *
     * @param marks 流程标志
     * @return 流程性质数据
     */
    List<DtoProjectFlowProperty> findByMarkIn(Collection<String> marks);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 流程性质实体
     */
    DtoProjectFlowProperty findOne(String id);
}
