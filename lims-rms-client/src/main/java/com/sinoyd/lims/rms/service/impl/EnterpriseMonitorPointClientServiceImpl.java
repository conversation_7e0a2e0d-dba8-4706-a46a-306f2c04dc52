package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoEnterpriseMonitorPoint;
import com.sinoyd.lims.rms.feign.IEnterpriseMonitorPointFeignService;
import com.sinoyd.lims.rms.service.IEnterpriseMonitorPointClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 企业点位client接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/11
 */
@Service
public class EnterpriseMonitorPointClientServiceImpl implements IEnterpriseMonitorPointClientService {

    private IEnterpriseMonitorPointFeignService enterpriseMonitorPointFeignService;

    @Override
    public List<DtoEnterpriseMonitorPoint> findByIds(Collection<String> ids) {
        RestResponse<List<DtoEnterpriseMonitorPoint>> restResponse = enterpriseMonitorPointFeignService.findByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    public void setEnterpriseMonitorPointFeignService(IEnterpriseMonitorPointFeignService enterpriseMonitorPointFeignService) {
        this.enterpriseMonitorPointFeignService = enterpriseMonitorPointFeignService;
    }
}