package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoMonitorPlan2Point;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 监测计划和点位关联feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/21
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/monitorPlan2Point",
        configuration = {FeignConfig.class}
)
public interface IMonitorPlan2PointFeignService {

    /**
     * 根据监测计划id查询
     *
     * @param planIds 监测计划id集合
     * @return 响应结果
     */
    @PostMapping("/planIds")
    RestResponse<List<DtoMonitorPlan2Point>> findByPlanIds(@RequestBody Collection<String> planIds);

}
