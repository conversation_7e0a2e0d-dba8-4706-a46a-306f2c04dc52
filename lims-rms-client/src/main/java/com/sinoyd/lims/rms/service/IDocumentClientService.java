package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoDocument;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 附件对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/10
 */
public interface IDocumentClientService {

    /**
     * 根据给定的对象ID集合和文档类型查询最新的文档信息
     *
     * @param objectIds    包含对象ID的集合
     * @param documentType 文档类型
     * @return 返回包含文档信息的Map对象，键为文档ID，值为对应的DtoDocument对象
     */
    Map<String, DtoDocument> queryLatestDocument(Collection<String> objectIds,
                                                 String documentType);

    /**
     * 根据对象标识集合查询
     *
     * @param objectIds 对象标识结合
     * @return 文件列表
     */
    List<DtoDocument> findByObjectIdIn(Collection<String> objectIds);

    /**
     * 根据对象id和文档类型查询
     *
     * @param objectIds 对象id集合
     * @param docType   文档类型
     * @return 文档集合
     */
    List<DtoDocument> findByObjectIdsAndDocType(List<String> objectIds, String docType);

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 文件数据集合
     */
    List<DtoDocument> findAll(Collection<String> ids);


    /**
     * 删除
     *
     * @param ids id集合
     * @return 删除条数
     */
    Integer deleteByIds(List<String> ids);

    /**
     * 保存
     *
     * @param documents 文档集合
     * @return 保存后的文档集合
     */
    List<DtoDocument> save(List<DtoDocument> documents);
}
