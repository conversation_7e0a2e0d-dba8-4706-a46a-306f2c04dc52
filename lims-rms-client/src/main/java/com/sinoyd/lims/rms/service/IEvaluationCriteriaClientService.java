package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoEvaluationCriteria;

import java.util.Collection;
import java.util.List;

/**
 * 评价标准 对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
public interface IEvaluationCriteriaClientService {
    /**
     * 根据标识集合查询
     *
     * @param ids id集合
     * @return 结果
     */
    List<DtoEvaluationCriteria> findByIds(Collection<String> ids);
}
