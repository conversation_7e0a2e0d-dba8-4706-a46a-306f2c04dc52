package com.sinoyd.lims.rms.dto;

import lombok.Data;

/**
 * 仪器使用用途对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/12
 */
@Data
public class DtoInstrumentOutPurpose {

    /**
     * 主键id
     */
    private String id;

    /**
     * 仪器出入库id
     */
    private String instrumentInventoryId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 用途(手动输入的)
     */
    private String otherPurpose;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目业务人员名称（项目负责人名称）
     */
    private String merchandiserName;
}
