package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.criteria.InstrumentOutDeviceCriteria;
import com.sinoyd.lims.rms.dto.DtoInstrumentOutDevice;
import com.sinoyd.lims.rms.feign.IInstrumentOutDeviceFeignService;
import com.sinoyd.lims.rms.service.IInstrumentOutDeviceClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 仪器出入库设备对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/18
 */
@Service
public class InstrumentOutDeviceClientServiceImpl implements IInstrumentOutDeviceClientService {

    private IInstrumentOutDeviceFeignService instrumentOutDeviceFeignService;

    @Override
    public List<DtoInstrumentOutDevice> findFeignPage(PageBean<DtoInstrumentOutDevice> pb,
                                                      InstrumentOutDeviceCriteria criteria) {
        RestResponse<List<DtoInstrumentOutDevice>> response = instrumentOutDeviceFeignService
                .findFeignPage(pb.getPageNo(), pb.getRowsPerPage(), pb.getSort(), criteria);
        if (response.isSuccess()) {
            pb.setRowsCount(response.getCount());
            return response.getData();
        }
        return new ArrayList<>();
    }

    @Autowired
    public void setInstrumentOutDeviceFeignService(IInstrumentOutDeviceFeignService instrumentOutDeviceFeignService) {
        this.instrumentOutDeviceFeignService = instrumentOutDeviceFeignService;
    }
}
