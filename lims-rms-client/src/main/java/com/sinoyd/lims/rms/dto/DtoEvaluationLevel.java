package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.Date;

/**
 * 评价等级
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@Data
public class DtoEvaluationLevel {

    /**
     * 主键id
     */
    private String id;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 等级名称（条件项名称）
     */
    private String name;

    /**
     * 条件描述
     */
    private String description;

    /**
     * 排序值（条件编码）
     */
    private Integer orderNum;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}
