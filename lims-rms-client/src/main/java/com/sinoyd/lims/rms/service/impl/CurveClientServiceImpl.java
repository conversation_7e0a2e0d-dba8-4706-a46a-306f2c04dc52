package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoCurve;
import com.sinoyd.lims.rms.feign.ICurveFeignService;
import com.sinoyd.lims.rms.service.ICurveClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 曲线信息对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/08
 */
@Service
public class CurveClientServiceImpl implements ICurveClientService {

    private ICurveFeignService curveFeignService;

    @Override
    public List<DtoCurve> findByIds(Collection<String> ids) {
        RestResponse<List<DtoCurve>> response = curveFeignService.findByIds(ids);
        return response.isSuccess() ? response.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoCurve> update(List<DtoCurve> curveList) {
        RestResponse<List<DtoCurve>> response = curveFeignService.update(curveList);
        if (!response.isSuccess()) {
            throw new RuntimeException("RMS更新曲线信息失败!");
        }
        return response.getData();
    }

    @Autowired
    public void setCurveFeignService(ICurveFeignService curveFeignService) {
        this.curveFeignService = curveFeignService;
    }
}
