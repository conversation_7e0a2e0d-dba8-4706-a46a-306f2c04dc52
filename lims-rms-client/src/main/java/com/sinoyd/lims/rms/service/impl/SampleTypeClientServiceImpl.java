package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import com.sinoyd.lims.rms.feign.ISampleTypeFeignService;
import com.sinoyd.lims.rms.service.ISampleTypeClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 检测类型对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/4
 */
@Service
public class SampleTypeClientServiceImpl implements ISampleTypeClientService {

    private ISampleTypeFeignService sampleTypeFeignService;

    @Override
    public List<DtoSampleType> findAllByIds(Collection<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        RestResponse<List<DtoSampleType>> restResponse = sampleTypeFeignService.findAllByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public Map<String, String> findMapByIds(Collection<String> ids) {
        List<DtoSampleType> dataList = findAllByIds(ids);
        return dataList.stream().collect(java.util.stream.Collectors.toMap(DtoSampleType::getId, DtoSampleType::getTypeName));
    }

    @Override
    public DtoSampleType findOne(String sampleTypeId) {
        RestResponse<DtoSampleType> restResponse = sampleTypeFeignService.findOne(sampleTypeId);
        if (!restResponse.isSuccess()) {
            throw new BaseException("查询检测类型发生错误");
        }
        return restResponse.getData();
    }

    @Override
    public List<DtoSampleType> findAllBigSampleType() {
        RestResponse<List<DtoSampleType>> restResponse = sampleTypeFeignService.findAllBigSampleType();
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }


    @Autowired
    @Lazy
    public void setSampleTypeFeignService(ISampleTypeFeignService sampleTypeFeignService) {
        this.sampleTypeFeignService = sampleTypeFeignService;
    }

}