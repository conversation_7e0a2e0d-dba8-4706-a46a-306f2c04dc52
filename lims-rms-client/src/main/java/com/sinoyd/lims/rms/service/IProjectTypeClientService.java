package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoProjectFlowProperty;
import com.sinoyd.lims.rms.dto.DtoProjectType;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目类型对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
public interface IProjectTypeClientService {

    /**
     * 根据id查询项目类型
     *
     * @param ids 项目类型id集合
     * @return 项目类型集合
     */
    List<DtoProjectType> findAllByIds(Collection<String> ids);

    /**
     * 查询所有的项目类型
     *
     * @return 响应结果
     */
    List<DtoProjectType> findAll();

    /**
     * 根据id集合查询，并把返回结果映射成Map格式
     *
     * @param ids id集合
     * @return Map<项目类型id, 项目类型名称>
     */
    Map<String, String> findMapByIds(Collection<String> ids);

    /**
     * 根据项目类型id查询
     *
     * @param id 项目类型id
     * @return 响应结果
     */
    DtoProjectType findOne(String id);

    /**
     * 加载项目类型下的流程性质
     *
     * @param projectTypeId 项目类型id
     * @return 流程性质实体集合
     */
    List<DtoProjectFlowProperty> loadFlowProperty(String projectTypeId);
}
