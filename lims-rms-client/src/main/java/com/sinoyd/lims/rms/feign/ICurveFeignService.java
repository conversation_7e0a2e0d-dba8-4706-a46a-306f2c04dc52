package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoCurve;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 曲线Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/11
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/curve",
        configuration = {FeignConfig.class}
)
public interface ICurveFeignService {

    /**
     * 根据id集合查询标准曲线信息
     *
     * @param ids id集合
     * @return 标准曲线数据
     */
    @PostMapping("/ids")
    RestResponse<List<DtoCurve>> findByIds(@RequestBody Collection<String> ids);

    /**
     * 修改标准曲线
     *
     * @param curveList 标准曲线
     * @return RestResponse<DtoCurveTemp>
     */
    @PutMapping
    RestResponse<List<DtoCurve>> update(@RequestBody List<DtoCurve> curveList);
}
