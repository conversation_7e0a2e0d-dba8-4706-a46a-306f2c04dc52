package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 监测计划点位关联对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/21
 */
@Data
public class DtoMonitorPlan2Point {

    /**
     * 主键
     */
    private String id;

    /**
     * 监测计划id
     */
    private String monitorPlanId;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 周期
     */
    private Integer period;

    /**
     * 点位id
     */
    private String pointId;

    /**
     * 假删标识
     */
    private Boolean isDeleted;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date modifyDate;

    /**
     * 监测计划名称
     */
    private String planName;

    /**
     * 监测计划年份
     */
    private Integer planYear;

    /**
     * 监测计划月份
     */
    private Integer planMonth;

    /**
     * 点位名称
     */
    private String pointName;

    /**
     * 点位编号
     */
    private String pointSerialNo;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 分析项目
     */
    private String analyzeItemName;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 监测类型大类id
     */
    private String bigSampleTypeId;

    /**
     * 需要添加的关联点位id集合
     */
    private List<String> pointIds;

    /**
     * 新增时的所选测试项目数据
     */
    private List<DtoMonitorPlanPoint2Test> point2Tests;

    /**
     * 监测计划点位id集合（用于新增时，添加测试项目时用到的传参）
     */
    private List<String> plan2PointIds;
}