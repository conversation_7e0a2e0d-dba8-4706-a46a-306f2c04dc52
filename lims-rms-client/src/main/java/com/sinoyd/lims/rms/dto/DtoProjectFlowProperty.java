package com.sinoyd.lims.rms.dto;

import lombok.Data;

/**
 * 流程性质对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/26
 */
@Data
public class DtoProjectFlowProperty {

    /**
     * 主键id
     */
    private String id;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 标志
     */
    private String mark;

    /**
     * 工作流key
     */
    private String workflowKey;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 工作流名称
     */
    private String workflowName;

}