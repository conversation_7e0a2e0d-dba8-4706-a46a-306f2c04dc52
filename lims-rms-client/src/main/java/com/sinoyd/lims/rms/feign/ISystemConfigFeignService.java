package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoSystemConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 系统信息配置feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/13
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/system",
        configuration = {FeignConfig.class}
)
public interface ISystemConfigFeignService {

    /**
     * 查询系统信息配置详情
     *
     * @return 结果
     */
    @GetMapping
    RestResponse<DtoSystemConfig> findSystemConfig();
}
