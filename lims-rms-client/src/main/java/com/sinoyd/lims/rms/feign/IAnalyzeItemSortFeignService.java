package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoAnalyzeItemSort;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 分析项目排序Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/26
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/analyzeItemSort",
        configuration = {FeignConfig.class}
)
public interface IAnalyzeItemSortFeignService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoAnalyzeItemSort> find(@PathVariable(name = "id") String id);

    /**
     * 根据id集合查询
     *
     * @param ids 主键集合
     * @return 结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoAnalyzeItemSort>> findByIds(@RequestBody Collection<String> ids);
}
