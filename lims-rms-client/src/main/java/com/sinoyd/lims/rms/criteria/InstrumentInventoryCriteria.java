package com.sinoyd.lims.rms.criteria;

import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;

/**
 * 仪器出入库对外查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/12
 */
@Data
public class InstrumentInventoryCriteria extends BaseCriteria {

    /**
     * 申请日期(开始)
     */
    private String applyDateStart;

    /**
     * 申请日期(结束)
     */
    private String applyDateEnd;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 仪器关键字: 根据仪器名称编号模糊检索
     */
    private String instrumentKey;

    /**
     * 仪器入库状态，参考EnumInstrumentInStatus [RMS]
     */
    private Integer inStatus;

    /**
     * 仪器id
     */
    private String instrumentId;

    @Override
    public String getCondition() {
        return null;
    }
}
