package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.criteria.InstrumentInventoryCriteria;
import com.sinoyd.lims.rms.dto.DtoInstrumentInventory;
import com.sinoyd.lims.rms.feign.IInstrumentInventoryFeignService;
import com.sinoyd.lims.rms.service.IInstrumentInventoryClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 仪器出入库对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/12
 */
@Service
public class InstrumentInventoryClientServiceImpl implements IInstrumentInventoryClientService {

    private IInstrumentInventoryFeignService instrumentInventoryFeignService;

    @Override
    public List<DtoInstrumentInventory> findByPage(PageBean<DtoInstrumentInventory> pageBean, InstrumentInventoryCriteria criteria) {
        RestResponse<List<DtoInstrumentInventory>> response = instrumentInventoryFeignService.findFeignPage
                (pageBean.getPageNo(), pageBean.getRowsPerPage(), pageBean.getSort(), criteria);
        if (response.isSuccess()) {
            pageBean.setRowsCount(response.getCount());
            return response.getData();
        }
        return new ArrayList<>();
    }

    @Autowired
    public void setInstrumentInventoryFeignService(IInstrumentInventoryFeignService instrumentInventoryFeignService) {
        this.instrumentInventoryFeignService = instrumentInventoryFeignService;
    }
}
