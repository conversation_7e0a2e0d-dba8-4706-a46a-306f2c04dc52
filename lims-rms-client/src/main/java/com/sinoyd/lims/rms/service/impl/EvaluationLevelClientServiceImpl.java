package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.lims.rms.dto.DtoEvaluationLevel;
import com.sinoyd.lims.rms.feign.IEvaluationLevelFeignService;
import com.sinoyd.lims.rms.service.IEvaluationLevelClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 评价等级 对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@Service
public class EvaluationLevelClientServiceImpl implements IEvaluationLevelClientService {

    private IEvaluationLevelFeignService evaluationLevelFeignService;

    @Override
    public List<DtoEvaluationLevel> findByIds(Collection<String> ids) {
        if(StringUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        RestResponse<List<DtoEvaluationLevel>> restResponse = evaluationLevelFeignService.findByIds(ids);
        if(!restResponse.isSuccess()){
            return new ArrayList<>();
        }
        return restResponse.getData();
    }

    @Autowired
    @Lazy
    public void setEvaluationLevelFeignService(IEvaluationLevelFeignService evaluationLevelFeignService) {
        this.evaluationLevelFeignService = evaluationLevelFeignService;
    }
}
