package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoInstrument;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 仪器feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/11
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/instrument",
        configuration = {FeignConfig.class}
)
public interface IInstrumentFeignService {

    /**
     * 根据仪器id查询仪器数据
     *
     * @param ids 仪器id集合
     * @return 仪器数据
     */
    @PostMapping("/ids")
    RestResponse<List<DtoInstrument>> findByIds(@RequestBody Collection<String> ids);
}
