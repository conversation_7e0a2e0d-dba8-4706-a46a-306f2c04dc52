package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoSampleGroupRuleConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 样品分组Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/10
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/sampleGroupRuleConfig",
        configuration = {FeignConfig.class}
)
public interface ISampleGroupRuleConfigFeignService {

    /**
     * 根据分组规则id集合查询分组数据Map
     *
     * @param ruleIds 规则id集合
     * @return 规则id对应的数据Map
     */
    @PostMapping("/map/rule/ids")
    RestResponse<Map<String, List<DtoSampleGroupRuleConfig>>> findMapByRuleIds(@RequestBody Collection<String> ruleIds);
}
