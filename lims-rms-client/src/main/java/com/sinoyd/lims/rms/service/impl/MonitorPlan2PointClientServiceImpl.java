package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.lims.rms.dto.DtoMonitorPlan2Point;
import com.sinoyd.lims.rms.feign.IMonitorPlan2PointFeignService;
import com.sinoyd.lims.rms.service.IMonitorPlan2PointClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 监测计划和点位关联client实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/21
 */
@Service
public class MonitorPlan2PointClientServiceImpl implements IMonitorPlan2PointClientService {

    private IMonitorPlan2PointFeignService monitorPlan2PointFeignService;

    @Override
    public List<DtoMonitorPlan2Point> findByPlanIds(Collection<String> planIds) {
        RestResponse<List<DtoMonitorPlan2Point>> restResponse = monitorPlan2PointFeignService.findByPlanIds(planIds);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    public void setMonitorPlan2PointFeignService(IMonitorPlan2PointFeignService monitorPlan2PointFeignService) {
        this.monitorPlan2PointFeignService = monitorPlan2PointFeignService;
    }
}