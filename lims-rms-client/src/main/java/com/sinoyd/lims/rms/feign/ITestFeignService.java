package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoTest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 测试项目Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/1
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/test",
        configuration = {FeignConfig.class}
)
public interface ITestFeignService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 响应结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoTest>> findAllByIds(@RequestBody Collection<String> ids);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 响应结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoTest> findOne(@PathVariable("id") String id);

    /**
     * 根据检测类型id查询（如果是小类id，自动转换为大类id查询）
     *
     * @param sampleTypeId 检测类型id
     * @return 响应结果
     */
    @GetMapping("/sampleType/{sampleTypeId}")
    RestResponse<List<DtoTest>> findBySampleTypeId(@PathVariable("sampleTypeId") String sampleTypeId);

    /**
     * 查询所有可用的(未删除、未作废)测试项目
     *
     * @return 响应结果
     */
    @GetMapping("/valid/all")
    RestResponse<List<DtoTest>> findValidList();

    /**
     * 修正使用次数
     *
     * @param ids id集合
     * @return 响应结果
     */
    @PostMapping("/usageNum")
    RestResponse<List<DtoTest>> updateUsageNum(@RequestBody List<String> ids);
}
