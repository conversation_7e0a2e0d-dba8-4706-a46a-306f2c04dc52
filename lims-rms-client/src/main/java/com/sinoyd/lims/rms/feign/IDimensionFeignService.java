package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoDimension;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 量纲Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/dimension",
        configuration = {FeignConfig.class}
)
public interface IDimensionFeignService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoDimension> find(@PathVariable(name = "id") String id);

    /**
     * 根据标识集合查询
     *
     * @param ids 集合查询
     * @return 结果
     */
    @PostMapping("/ids")
    RestResponse<List<DtoDimension>> findByIds(@RequestBody Collection<String> ids);
}
