package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoAnalyzeItemSort;

import java.util.Collection;
import java.util.List;

/**
 * 分析项目排序对外服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/26
 */
public interface IAnalyzeItemSortClientService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 结果
     */
    DtoAnalyzeItemSort find(String id);

    /**
     * 根据id集合查询
     *
     * @param ids 主键集合
     * @return 结果
     */
    List<DtoAnalyzeItemSort> findByIds(Collection<String> ids);
}
