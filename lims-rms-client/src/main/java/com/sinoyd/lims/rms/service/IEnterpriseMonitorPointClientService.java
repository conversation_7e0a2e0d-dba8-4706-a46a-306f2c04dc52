package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoEnterpriseMonitorPoint;

import java.util.Collection;
import java.util.List;

/**
 * 企业点位client接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/11
 */
public interface IEnterpriseMonitorPointClientService {

    /**
     * 跟模id集合查询
     *
     * @param ids id集合
     * @return 响应结果
     */
    List<DtoEnterpriseMonitorPoint> findByIds(Collection<String> ids);
}
