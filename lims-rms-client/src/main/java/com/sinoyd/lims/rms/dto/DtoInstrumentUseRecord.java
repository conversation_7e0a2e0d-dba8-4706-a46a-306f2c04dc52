package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.DateUtil;
import lombok.Data;

import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 仪器使用记录对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/10
 */
@Data
public class DtoInstrumentUseRecord {

    /**
     * 主键id
     */
    private String id;

    /**
     * 开始使用时间
     */
    @JsonFormat(pattern = DateUtil.FULL)
    private Date startTime;

    /**
     * 结束使用时间
     */
    @JsonFormat(pattern = DateUtil.FULL)
    private Date endTime;

    /**
     * 使用人id
     */
    private String usePersonId;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 大气压
     */
    private String pressure;

    /**
     * 表单id（送样单-采样、领样单-现场分析、检测单-实验室）
     */
    private String objectId;

    /**
     * 使用对象类型（枚举EnumInsUseObjType：1采样，2：实验室分析，4：现场分析）
     */
    private Integer objectType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仪器id集合（用于新增仪器使用记录时传参）
     */
    private List<String> instrumentIds;

    /**
     * 使用记录下关联的测试项目数据
     */
    private List<DtoInstrumentUseRecord2Test> instrumentUseRecord2Tests = new ArrayList<>();
}
