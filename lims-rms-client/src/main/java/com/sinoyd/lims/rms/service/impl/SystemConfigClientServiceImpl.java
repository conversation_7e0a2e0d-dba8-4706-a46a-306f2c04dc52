package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.rms.dto.DtoSystemConfig;
import com.sinoyd.lims.rms.feign.ISystemConfigFeignService;
import com.sinoyd.lims.rms.service.ISystemConfigClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 系统信息配置对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/11/13
 */
@Service
public class SystemConfigClientServiceImpl implements ISystemConfigClientService {

    private ISystemConfigFeignService systemConfigFeignService;

    @Override
    public DtoSystemConfig findSystemConfig() {
        RestResponse<DtoSystemConfig> response = systemConfigFeignService.findSystemConfig();
        if (!response.isSuccess()) {
            throw new BaseException("查询系统信息配置失败:" + response.getMsg());
        }
        return response.getData();
    }

    @Autowired
    public void setSystemConfigFeignService(ISystemConfigFeignService systemConfigFeignService) {
        this.systemConfigFeignService = systemConfigFeignService;
    }
}
