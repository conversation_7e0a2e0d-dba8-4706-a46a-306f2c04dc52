package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoProjectFlowProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 项目流程性质Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/projectFlowProperty",
        configuration = {FeignConfig.class}
)
public interface IProjectFlowPropertyFeignService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 流程性质集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoProjectFlowProperty>> findAllByIds(@RequestBody Collection<String> ids);

    /**
     * 根据流程标志查询
     *
     * @param marks 流程标志
     * @return 流程性质集合
     */
    @PostMapping("/marks")
    RestResponse<List<DtoProjectFlowProperty>> findAllByMarks(@RequestBody Collection<String> marks);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 流程性质实体
     */
    @GetMapping("/{id}")
    RestResponse<DtoProjectFlowProperty> findOne(@PathVariable("id") String id);
}
