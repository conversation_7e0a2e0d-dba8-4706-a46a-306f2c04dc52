package com.sinoyd.lims.rms.dto;

import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 编号配置规则对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/17
 */
@Data
public class DtoSerialIdentifierConfig {

    /**
     * 主键id
     */
    private String id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型，常量lims_rms_serialType编码
     */
    private String configTypeCode;

    /**
     * 配置规则
     */
    private String configRule;

    /**
     * 项目类型id
     */
    private String projectTypeId;

    /**
     * 质控样id(配置)
     */
    private String qcInfoId;

    /**
     * 排序号
     */
    private Integer orderNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 假删
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 配置适用范围，用于存储项目类型、质控样类型等信息
     */
    private Map<String, Object> configRange = new HashMap<>();
}