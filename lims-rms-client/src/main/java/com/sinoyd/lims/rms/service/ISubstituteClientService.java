package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoSubstitute;

import java.util.Collection;
import java.util.Map;

/**
 * 替代物对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/9
 */
public interface ISubstituteClientService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 实体
     */
    DtoSubstitute findOne(String id);

    /**
     * 根据id查询，并将结果映射成Map
     *
     * @param ids 主键集合
     * @return 响应结果
     */
    Map<String, DtoSubstitute> findAllMap(Collection<String> ids);
}
