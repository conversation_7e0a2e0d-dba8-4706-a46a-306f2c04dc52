package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoInstrumentUseRecord;

import java.util.Collection;
import java.util.List;

/**
 * 仪器使用记录对外服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/10
 */
public interface IInstrumentUseRecordClientService {

    /**
     * 仪器使用记录
     *
     * @param objectId 对象id
     * @param type     类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdAndType(String objectId, Integer type);


    /**
     * 对象id集合与仪器使用类型查询仪器使用记录
     *
     * @param objectIds 对象id集合
     * @param type     类型
     * @return 仪器使用记录
     */
    List<DtoInstrumentUseRecord> findByObjectIdInAndType(Collection<String> objectIds, Integer type);
}
