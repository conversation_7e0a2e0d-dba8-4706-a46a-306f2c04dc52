package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.criteria.ReagentConfigCriteria;
import com.sinoyd.lims.rms.dto.DtoReagentConfig;
import com.sinoyd.lims.rms.feign.IReagentConfigFeignService;
import com.sinoyd.lims.rms.service.IReagentConfigClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 试剂配置记录对外服务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/13
 */
@Service
public class ReagentConfigClientServiceImpl implements IReagentConfigClientService {

    private IReagentConfigFeignService reagentConfigFeignService;

    @Override
    public List<DtoReagentConfig> findByPage(PageBean<DtoReagentConfig> pb, ReagentConfigCriteria criteria) {
        RestResponse<List<DtoReagentConfig>> response = reagentConfigFeignService.findPostPage
                (pb.getPageNo(), pb.getRowsPerPage(), pb.getSort(), criteria);
        if (response.isSuccess()) {
            pb.setRowsCount(response.getCount());
            return response.getData();
        }
        return new ArrayList<>();
    }

    @Autowired
    public void setReagentConfigFeignService(IReagentConfigFeignService reagentConfigFeignService) {
        this.reagentConfigFeignService = reagentConfigFeignService;
    }
}
