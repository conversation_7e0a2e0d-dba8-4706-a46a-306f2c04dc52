package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.DateUtil;
import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 仪器出入库设备信息对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/12
 */
@Data
public class DtoInstrumentOutDevice {


    /**
     * 主键id
     */
    private String id;

    /**
     * 仪器出入库id
     */
    private String instrumentInventoryId;

    /**
     * 设备id(仪器id)
     */
    private String deviceId;

    /**
     * 出库人id
     */
    private String outPersonId;

    /**
     * 出库日期
     */
    @JsonFormat(pattern = DateUtil.YEAR)
    private Date outDate;

    /**
     * 出库是否合格
     */
    private Boolean isOutEligible;

    /**
     * 出库是否确认
     */
    private Boolean isOutConfirmed;

    /**
     * 入库人id
     */
    private String inPersonId;

    /**
     * 入库日期
     */
    @JsonFormat(pattern = DateUtil.YEAR)
    private Date inDate;

    /**
     * 入库是否合格
     */
    private Boolean isInEligible;

    /**
     * 仪器设备名称
     */
    private String deviceName;

    /**
     * 仪器设备编号
     */
    private String deviceCode;

    /**
     * 仪器设备规格型号
     */
    private String deviceModel;

    /**
     * 仪器设备名称和型号组合显示
     */
    private String deviceNameWithModel;

    /**
     * 出库人名称
     */
    private String outPersonName;

    /**
     * 入库人名称
     */
    private String inPersonName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目负责人名称
     */
    private String projectLeaderName;

    /**
     * 仪器用途
     */
    private String instrumentPurpose;

    /**
     * 仪器用途数据集合
     */
    private List<DtoInstrumentOutPurpose> outPurposeList;
}
