package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoTestPositionPerson;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * 测试岗位关联人员Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/05/24
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/testPositionPerson",
        configuration = {FeignConfig.class}
)
public interface ITestPositionPersonFeignService {


    /**
     * 根据人员id集合查询岗位关联数据
     *
     * @param personIds 人员id集合
     * @return 岗位关联数据
     */
    @PostMapping("/person/ids")
    RestResponse<List<DtoTestPositionPerson>> queryByPersonIds(@RequestBody Collection<String> personIds);
}
