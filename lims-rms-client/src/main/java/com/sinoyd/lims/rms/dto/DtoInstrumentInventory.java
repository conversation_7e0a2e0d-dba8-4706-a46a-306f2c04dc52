package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.DateUtil;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 仪器出入库对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/12
 */
@Data
public class DtoInstrumentInventory {


    /**
     * 主键id
     */
    private String id;

    /**
     * 申请日期
     */
    @JsonFormat(pattern = DateUtil.YEAR)
    private Date applyDate;

    /**
     * 管理人员id
     */
    private String managerId;

    /**
     * 出库是否合格
     */
    private Boolean isOutEligible;

    /**
     * 入库是否合格
     */
    private Boolean isInEligible;

    /**
     * 出库是否确认
     */
    private Boolean isOutConfirmed;

    /**
     * 假删
     */
    private Boolean isDeleted;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;


    /**
     * 使用人员id集合
     */
    private List<String> userIds;

    /**
     * 用途集合
     */
    private List<DtoInstrumentOutPurpose> instrumentOutPurposeList;

    /**
     * 绑定设备集合
     */
    private List<DtoInstrumentOutDevice> deviceList;

    /**
     * 出库确认数量
     */
    private Long outConfirmCount;

    /**
     * 已入库数量
     */
    private Long inCount;

    /**
     * 出库设备总数
     */
    private Integer totalDeviceCount;

    /**
     * 使用人员名称，多个用英文逗号拼接
     */
    private String usePersonNames;

    /**
     * 管理人员名称
     */
    private String managerName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 仪器用途，多个用英文逗号拼接
     * 如果选的是项目就是项目名称，手填的直接获取 {@link DtoInstrumentOutPurpose}中属性otherPurpose值
     */
    private String purposes;

    /**
     * 仪器设备名称;
     */
    private String deviceNameModel;
}
