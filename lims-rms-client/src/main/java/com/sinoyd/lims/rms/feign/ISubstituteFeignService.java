package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoSubstitute;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.Map;

/**
 * 替代物管理 feign 接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/1/9
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/substitute",
        configuration = {FeignConfig.class}
)
public interface ISubstituteFeignService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 响应结果
     */
    @GetMapping("/{id}")
    RestResponse<DtoSubstitute> findOne(@PathVariable(name = "id") String id);

    /**
     * 根据id查询，并将结果映射成Map
     *
     * @param ids 主键集合
     * @return 响应结果
     */
    @PostMapping("/map")
    RestResponse<Map<String, DtoSubstitute>> findAllMap(@RequestBody Collection<String> ids);
}
