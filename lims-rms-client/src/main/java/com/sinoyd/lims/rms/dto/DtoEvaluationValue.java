package com.sinoyd.lims.rms.dto;

import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Id;
import java.util.Date;

/**
 * 评价限值对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/3/14
 */
@Data
public class DtoEvaluationValue {

    /**
     * 主键id
     */
    private String id;

    /**
     * 评价标准id
     */
    private String evaluationId;

    /**
     * 评价等级id
     */
    private String levelId;

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 上限
     */
    private String upperLimit;

    /**
     * 上限运算符
     */
    private String upperLimitSymbol;

    /**
     * 下限
     */
    private String lowerLimit;

    /**
     * 下限运算符
     */
    private String lowerLimitSymbol;

    /**
     * 备注
     */
    private String remark;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDate;
}