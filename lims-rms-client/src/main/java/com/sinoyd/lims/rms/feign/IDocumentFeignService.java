package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoDocument;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 附件Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/10
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/document",
        configuration = {FeignConfig.class}
)
public interface IDocumentFeignService {

    /**
     * 根据id查询
     *
     * @param ids id集合
     * @return 文件数据集合
     */
    @PostMapping("/ids")
    RestResponse<List<DtoDocument>> findAll(@RequestBody Collection<String> ids);

    /**
     * 根据给定的对象ID和文档类型查询最新的文档信息
     *
     * @param objectIds    包含对象ID的集合
     * @param documentType 文档类型
     * @return 返回包含文档信息的RestResponse对象
     */
    @PostMapping("/latest/{documentType}")
    RestResponse<Map<String, DtoDocument>> queryLatestDocument(@RequestBody Collection<String> objectIds,
                                                               @PathVariable("documentType") String documentType);

    /**
     * 根据业务id查询
     *
     * @param objectIds 业务id集合
     * @return 结果
     */
    @PostMapping("/objects")
    RestResponse<List<DtoDocument>> findByObjectIdIn(@RequestBody Collection<String> objectIds);

    /**
     * 根据对象id和文档类型查询
     *
     * @param objectIds 对象id集合
     * @param docType   文档类型
     * @return 文档集合
     */
    @PostMapping("/objects/{docType}")
    RestResponse<List<DtoDocument>> findByObjectIdsAndDocType(@RequestBody List<String> objectIds,
                                                              @PathVariable("docType") String docType);

    /**
     * 批量删除
     *
     * @param ids 需要删除的id集合
     * @return 删除的记录数
     */
    @DeleteMapping
    RestResponse<String> delete(@RequestBody List<String> ids);

    /**
     * 保存
     *
     * @param documents 文档集合
     * @return 结果
     */
    @PostMapping
    RestResponse<List<DtoDocument>> save(@RequestBody Collection<DtoDocument> documents);
}
