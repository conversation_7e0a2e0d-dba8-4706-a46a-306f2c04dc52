package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoDimension;

import java.util.Collection;
import java.util.List;

/**
 * 量纲对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
public interface IDimensionClientService {

    /**
     * 详情查询
     *
     * @param id 主键
     * @return 结果
     */
    DtoDimension findOne(String id);

    /**
     * 根据标识集合查询
     *
     * @param ids 集合查询
     * @return 结果
     */
    List<DtoDimension> findByIds(Collection<String> ids);
}
