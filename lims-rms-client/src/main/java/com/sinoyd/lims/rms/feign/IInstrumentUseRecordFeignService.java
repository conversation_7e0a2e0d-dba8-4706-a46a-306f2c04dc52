package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoInstrumentUseRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * 仪器使用记录feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/07/10
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/instrumentUse",
        configuration = {FeignConfig.class}
)
public interface IInstrumentUseRecordFeignService {

    /**
     * 仪器使用记录
     *
     * @param objectId 对象id
     * @param type     类型
     * @return 仪器使用记录
     */
    @GetMapping("/object")
    RestResponse<List<DtoInstrumentUseRecord>> findByObjectIdAndType(@RequestParam String objectId, @RequestParam Integer type);

    /**
     * 根据对象id集合和类型查询仪器使用记录
     *
     * @param objectIds 对象id集合
     * @param type      类型
     * @return 仪器使用记录
     */
    @PostMapping("/object/ids")
    RestResponse<List<DtoInstrumentUseRecord>> findByObjectIdInAndType(@RequestParam Collection<String> objectIds, @RequestParam Integer type);

}
