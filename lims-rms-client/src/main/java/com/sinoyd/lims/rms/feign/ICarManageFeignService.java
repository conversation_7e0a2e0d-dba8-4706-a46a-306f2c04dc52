package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoCarManage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 车辆管理Feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/23
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/carManage",
        configuration = {FeignConfig.class}
)
public interface ICarManageFeignService {

    /**
     * 查询详情
     *
     * @param id 数据id
     * @return 详情
     */
    @GetMapping("/{id}")
    RestResponse<DtoCarManage> findOne(@PathVariable(name = "id") String id);
}
