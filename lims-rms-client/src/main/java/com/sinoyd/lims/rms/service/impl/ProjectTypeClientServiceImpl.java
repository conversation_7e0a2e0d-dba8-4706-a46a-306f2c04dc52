package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.rms.dto.DtoProjectFlowProperty;
import com.sinoyd.lims.rms.dto.DtoProjectType;
import com.sinoyd.lims.rms.feign.IProjectTypeFeignService;
import com.sinoyd.lims.rms.service.IProjectTypeClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目类型对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@Service
public class ProjectTypeClientServiceImpl implements IProjectTypeClientService {

    private IProjectTypeFeignService projectTypeFeignService;

    @Override
    public List<DtoProjectType> findAllByIds(Collection<String> ids) {
        RestResponse<List<DtoProjectType>> restResponse = projectTypeFeignService.findAllByIds(ids);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public List<DtoProjectType> findAll() {
        RestResponse<List<DtoProjectType>> restResponse = projectTypeFeignService.findAll();
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Override
    public Map<String, String> findMapByIds(Collection<String> ids) {
        List<DtoProjectType> dataList = findAllByIds(ids);
        return dataList.stream().collect(java.util.stream.Collectors.toMap(DtoProjectType::getId, DtoProjectType::getName));
    }

    @Override
    public DtoProjectType findOne(String id) {
        RestResponse<DtoProjectType> restResponse = projectTypeFeignService.findOne(id);
        if (restResponse.isSuccess()) {
            return restResponse.getData();
        }
        throw new BaseException("查询检测类型详情发生错误");
    }

    @Override
    public List<DtoProjectFlowProperty> loadFlowProperty(String projectTypeId) {
        RestResponse<List<DtoProjectFlowProperty>> restResponse = projectTypeFeignService.loadFlowProperty(projectTypeId);
        return restResponse.isSuccess() ? restResponse.getData() : new ArrayList<>();
    }

    @Autowired
    @Lazy
    public void setProjectTypeFeignService(IProjectTypeFeignService projectTypeFeignService) {
        this.projectTypeFeignService = projectTypeFeignService;
    }
}