package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 车辆对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/05/23
 */
@Data
public class DtoCarManage {

    /**
     * 车辆类型名称
     */
    private String carTypeName;


    /**
     * 负责人姓名
     */
    private String managerName;

    /**
     * 主键id
     */
    private String id;

    /**
     * 车牌号码
     */
    private String carCode;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆类型（常量lims_rms_carType:轿车、货车、商务车、SUV）
     */
    private String carType;

    /**
     * 车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)
     */
    private Integer state;

    /**
     * 负责人Id（Guid）
     */
    private String managerId;

    /**
     * 购置日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date buyDate;

    /**
     * 发动机号码
     */
    private String engineCode;

    /**
     * 最近年检日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date annualReviewDate;

    /**
     * 年检周期（月）
     */
    private Integer annualReviewCycle;

    /**
     * 油耗
     */
    private String oilConsumption;

    /**
     * 备注
     */
    private String remark;

    /**
     * GPS型号
     */
    private String gpsModelNo;

    /**
     * GPS编号
     */
    private String gpsSerialNo;
}
