package com.sinoyd.lims.rms.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 企业点位对外实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/4/11
 */
@Data
@Accessors(chain = true)
public class DtoEnterpriseMonitorPoint {

    /**
     * 主键
     */
    private String id;

    /**
     * 企业id
     */
    private String enterpriseId;

    /**
     * 点位名称
     */
    private String pointName;

    /**
     * 点位编号
     */
    private String pointSerialNo;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 周期
     */
    private Integer period;

    /**
     * 所属测站id
     */
    private String stationId;

    /**
     * 等级编码，常量管理，常量类型 lims_rms_controlLevel
     */
    private String levelCode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 是否启用
     */
    private Boolean isEnabled = true;

    /**
     * 排序值
     */
    private Integer orderNum;

    /**
     * 点位经纬度合并
     */
    private String location;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 检测大类id
     */
    private String bigSampleTypeId;

    /**
     * 分析项目名称集合字符串，多个以逗号拼接
     */
    private String analyzeItemNames;

    /**
     * 点位下测试项目集合
     */
    private List<DtoEnterpriseMonitorPointTest> pointTests;
}