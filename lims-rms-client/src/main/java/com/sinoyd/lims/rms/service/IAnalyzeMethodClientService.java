package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoAnalyzeMethod;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 分析方法对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/06/24
 */
public interface IAnalyzeMethodClientService {


    /**
     * 根据id集合查询
     *
     * @param ids id集合
     * @return 分析方法集合
     */
    List<DtoAnalyzeMethod> findAll(Collection<String> ids);

    /**
     * 根据id集合查询分析方法Map
     *
     * @param ids id集合
     * @return 分析方法Map
     */
    Map<String, DtoAnalyzeMethod> findAllMap(Collection<String> ids);
}
