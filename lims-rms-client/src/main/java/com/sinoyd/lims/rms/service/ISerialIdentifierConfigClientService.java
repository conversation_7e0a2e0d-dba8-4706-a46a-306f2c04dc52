package com.sinoyd.lims.rms.service;

import com.sinoyd.lims.rms.dto.DtoSerialIdentifierConfig;

/**
 * 编号配置规则对外接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/17
 */
public interface ISerialIdentifierConfigClientService {

    /**
     * 根据配置类型查询
     *
     * @param configTypeCode 配置类型
     * @return 编号配置实体
     */
    DtoSerialIdentifierConfig findByConfigTypeCode(String configTypeCode);

    /**
     * 根据编号类型和项目类型id查询
     *
     * @param typeCode      编号类型
     * @param projectTypeId 项目类型id
     * @return 编号配置实体
     */
    DtoSerialIdentifierConfig findByTypeAndProjectTypeId(String typeCode,
                                                         String projectTypeId);

    /**
     * 根据编号类型和报告类型查询
     *
     * @param typeCode   编号类型
     * @param reportType 报告类型
     * @return 响应结果
     */
    DtoSerialIdentifierConfig findByTypeAndReportType(String typeCode,
                                                      Integer reportType);

    /**
     * 根据编号类型和质控信息id查询
     *
     * @param typeCode      编号类型
     * @param projectTypeId 项目类型id
     * @param qcInfoId      质控信息id
     * @return 编号配置实体
     */
    DtoSerialIdentifierConfig findByTypeAndQcInfoId(String typeCode,
                                                    String projectTypeId,
                                                    String qcInfoId);
}
