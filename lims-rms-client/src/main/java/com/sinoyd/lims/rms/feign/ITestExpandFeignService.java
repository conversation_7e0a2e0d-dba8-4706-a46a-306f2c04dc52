package com.sinoyd.lims.rms.feign;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.frame.client.config.FeignConfig;
import com.sinoyd.lims.rms.dto.DtoTestExpand;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 测试项目扩展feign接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/5/21
 */
@FeignClient(
        value = "${sinoyd.lims.serviceId:lims60-lim}",
        path = "api/rms/testExpand",
        configuration = {FeignConfig.class}
)
public interface ITestExpandFeignService {

    /**
     * 根据测试项目id查询，并将结果映射成Map
     *
     * @param testIds 测试项目id集合
     * @return 测试项目id和测试项目扩展信息映射
     */
    @PostMapping("/map/testId")
    RestResponse<Map<String, List<DtoTestExpand>>> findMapByTestId(@RequestBody Collection<String> testIds);
}
