package com.sinoyd.lims.rms.service;

import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.criteria.InstrumentOutDeviceCriteria;
import com.sinoyd.lims.rms.dto.DtoInstrumentOutDevice;

import java.util.List;

/**
 * 仪器出入库设备对外服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/03/18
 */
public interface IInstrumentOutDeviceClientService {

    /**
     * 分页查询（用于仪器出库确认列表）
     *
     * @param pb       分页参数
     * @param criteria 查询条件
     * @return 结果
     */
    List<DtoInstrumentOutDevice> findFeignPage(PageBean<DtoInstrumentOutDevice> pb,
                                               InstrumentOutDeviceCriteria criteria);
}
