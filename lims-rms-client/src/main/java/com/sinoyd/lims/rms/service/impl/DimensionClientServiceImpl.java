package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.lims.rms.dto.DtoDimension;
import com.sinoyd.lims.rms.feign.IDimensionFeignService;
import com.sinoyd.lims.rms.service.IDimensionClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 量纲对外接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/10/22
 */
@Service
public class DimensionClientServiceImpl implements IDimensionClientService {

    private IDimensionFeignService dimensionFeignService;

    @Override
    public DtoDimension findOne(String id) {
        RestResponse<DtoDimension> response = dimensionFeignService.find(id);
        return response.getData();
    }


    @Override
    public List<DtoDimension> findByIds(Collection<String> ids) {
        if(StringUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        RestResponse<List<DtoDimension>> response = dimensionFeignService.findByIds(ids);
        return response.isSuccess()?response.getData():new ArrayList<>();
    }

    @Autowired
    public void setDimensionFeignService(IDimensionFeignService dimensionFeignService) {
        this.dimensionFeignService = dimensionFeignService;
    }
}
