package com.sinoyd.lims.rms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 人员证书检测能力对外实体
 *
 * <AUTHOR>
 * @version 6.0.0
 * @since 2024/05/18
 */
@Data
public class DtoPersonCertificateAbility {

    /**
     * 主键id
     */
    private String id;

    /**
     * 测试项目Id
     */
    private String testId;

    /**
     * 证书Id
     */
    private String personCertId;

    /**
     * 证书获得日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date achieveDate;

    /**
     * 有效期至
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certEffectiveTime;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 分析方法标准编号
     */
    private String standardNo;

    /**
     * 检测类型id
     */
    private String sampleTypeId;

    /**
     * 检测类型上的图标
     */
    private String sampleTypeIcon;

    /**
     * 检测类型名称
     */
    private String sampleTypeName;

    /**
     * 人员证书
     */
    private String personCertCode;

    /**
     * 证书名称
     */
    private String personCertName;

    /**
     * 人员Id
     */
    private String personId;

    /**
     * 批量操作的id列表
     */
    private List<String> operationIds = new ArrayList<>();

    /**
     * 测试项目id传输数组
     */
    private List<String> testIds = new ArrayList<>();
}
