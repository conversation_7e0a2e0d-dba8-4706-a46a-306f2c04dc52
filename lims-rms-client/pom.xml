<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sinoyd.lims</groupId>
        <artifactId>lims-rms</artifactId>
        <version>${rms.version}-MS-SNAPSHOT</version>
    </parent>

    <artifactId>lims-rms-client</artifactId>
    <name>lims-rms-client</name>
    <description>LIMS资源管理子系统Client层</description>

    <build>
        <finalName>lims-rms-client</finalName>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.frame</groupId>
            <artifactId>frame-arch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sinoydframework.boot</groupId>
            <artifactId>sinoyd-boot-starter-frame-client</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>

</project>
