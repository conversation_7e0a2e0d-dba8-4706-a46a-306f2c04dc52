<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.lims</groupId>
    <artifactId>lims-rms</artifactId>
    <version>${rms.version}-MS-SNAPSHOT</version>
    <name>lims-rms</name>
    <description>LIMS 资源管理子系统</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <rms.version>6.0.24</rms.version>
        <base.version>6.0.25</base.version>
        <frame-arch.version>6.0.0-SNAPSHOT</frame-arch.version>
        <sinoydframework.version>5.2.1-SNAPSHOT</sinoydframework.version>
    </properties>

    <modules>
        <module>lims-rms-public</module>
        <module>lims-rms-arch</module>
        <module>lims-rms-impl</module>
        <module>lims-rms-excel</module>
        <module>lims-rms-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-rms-public</artifactId>
                <version>${rms.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-rms-arch</artifactId>
                <version>${rms.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-rms-impl</artifactId>
                <version>${rms.version}-MS-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-rms-excel</artifactId>
                <version>${rms.version}-MS-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.lims</groupId>
                <artifactId>lims-base-impl</artifactId>
                <version>${base.version}-MS-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sinoyd.frame</groupId>
                <artifactId>frame-arch</artifactId>
                <version>${frame-arch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sinoydframework.boot</groupId>
                <artifactId>sinoyd-boot-starter-frame-client</artifactId>
                <version>${sinoydframework.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>
