package com.sinoyd.base.service.impl;

import com.jsoniter.JsonIterator;
import com.jsoniter.output.JsonStream;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.base.configuration.FilePropertyConfig;
import com.sinoyd.base.constants.IBaseConstants;
import com.sinoyd.base.enums.EnumApplicationModule;
import com.sinoyd.base.service.CommonService;
import com.sinoyd.base.vo.*;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.boot.frame.client.enums.EnumOrgType;
import com.sinoyd.common.preview.DocumentPreviewFactory;
import com.sinoyd.common.utils.FileUtil;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.QrCodeUtil;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.criteria.DepartmentCriteria;
import com.sinoyd.frame.criteria.OrgCriteria;
import com.sinoyd.frame.dto.*;
import com.sinoyd.frame.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 通用服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class CommonServiceImpl implements CommonService {

    private UserService userService;

    private RoleService roleService;

    private OrgService orgService;

    private AuthorizeService authorizeService;

    private DepartmentService departmentService;

    private AreaService areaService;

    private FilePropertyConfig filePropertyConfig;

    private FilePathConfig filePathConfig;

    private ConfigService configService;

    @Override
    public List<String> queryAreaIdsByParentId(String parentId) {
        if (StringUtils.isEmpty(parentId)) {
            return new ArrayList<>();
        }
        return loadChildAreaIds(Stream.of(parentId).collect(Collectors.toList()), areaService.findAreasByCenterConfig());
    }

    @Override
    public void previewDocument(DocumentPreviewVO vo, HttpServletResponse response) {
        DocumentPreviewFactory.previewAsPDF(filePropertyConfig.getFilePath(), vo, response);
    }

    @Override
    public List<GenericDropdownVO> loadEnumDropdown(String moduleName, String enumName) {
        List<GenericDropdownVO> voList = new ArrayList<>();
        try {
            EnumApplicationModule appModule = EnumApplicationModule.getEnumItem(moduleName);
            Class<Enum> clazz = (Class<Enum>) Class.forName(appModule.getEnumPackage() + "." + enumName);
            Enum[] enums = clazz.getEnumConstants();
            Method getValue = clazz.getMethod("getValue");
            boolean hasTag = Arrays.stream(clazz.getMethods()).anyMatch(m -> m.getName().equals("getTag"));
            boolean hasStyle = Arrays.stream(clazz.getMethods()).anyMatch(m -> m.getName().equals("getStyle"));
            boolean hasLabel = Arrays.stream(clazz.getMethods()).anyMatch(m -> m.getName().equals("getLabel"));
            for (Enum em : enums) {
                String name = em.name();
                if(hasLabel){
                    name = clazz.getMethod("getLabel").invoke(em).toString();
                }
                Object value = getValue.invoke(em);

                GenericDropdownVO vo = new GenericDropdownVO().setValue(value).setLabel(name);
                Map<String, Object> extendMap = new HashMap<>();
                if (hasTag) {
                    extendMap.put("tag", clazz.getMethod("getTag").invoke(em).toString());
                }
                if (hasStyle) {
                    extendMap.put("style", clazz.getMethod("getStyle").invoke(em).toString());
                }
                voList.add(vo.setExtendMap(extendMap));
            }


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException(String.format("获取枚举[%s]出错", enumName));
        }
        return voList;
    }

    @Override
    public DocumentPathVO getDocumentPathFromXml(String code, Map<String, Object> map) {
        DocumentPathVO vo = new DocumentPathVO();
        PathConfigVO dtoPathConfig = getXmlFilePathConfigByCode(code);
        if (StringUtils.isNull(dtoPathConfig)) {
            throw new BaseException("尚未配置附件归档路径");
        }
        String className = dtoPathConfig.getClassName();
        String methodName = dtoPathConfig.getMethod();
        String placeholder = dtoPathConfig.getPlaceholder();
        if (StringUtils.isEmpty(className) || StringUtils.isEmpty(methodName) || StringUtils.isEmpty(placeholder)) {
            throw new BaseException("编码[" + code + "]对应的附件归档路径配置不正确");
        }

        //处理相应的数据源
        String path = dtoPathConfig.getPath();
        try {
            Class clazz = Class.forName(className);
            Method method = Arrays.stream(clazz.getMethods()).filter(m -> m.getName().equals(methodName))
                    .findFirst().orElseThrow(() -> new BaseException("文件路径配置不正确"));
            Object data = method.invoke(SpringContextAware.getBean(clazz), map.values().toArray());
            //如果返回的直接是字符串，直接替换
            if (data instanceof String) {
                path = path.replace("{" + placeholder + "}", String.valueOf(data));
            } else {
                //根据占位符得出相应的数据字段名称
                String[] fieldNames = placeholder.split(",");
                if (data != null) {
                    Map<String, Object> dataMap = JsonIterator.deserialize(JsonStream.serialize(data), Map.class);
                    for (String fieldName : fieldNames) {
                        String value = String.valueOf(dataMap.get(fieldName));
                        if (StringUtils.isEmpty(value)) {
                            value = "";
                        }
                        path = path.replace("{" + fieldName + "}", value.trim().replace("/", "-"));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("从XML中获取文件路径失败");
        }
        vo.setPath(path);
        return vo;
    }

    @Override
    public String findConfigValue(String configParamKey) {
        DtoConfigCenter dtoConfigCenter = configService.findByKey(configParamKey);
        if (dtoConfigCenter == null) {
            throw new BaseException(String.format("系统中尚未配置对应的参数[%s]", configParamKey));
        }
        return dtoConfigCenter.getConfigValue();
    }

    @Override
    public String createQrCode(String content, int size) {
        BufferedImage qrCodeImage = QrCodeUtil.createImageCode(content, "UTF-8", size);
        return FileUtil.bufferImage2Base64(qrCodeImage);
    }

    @Override
    @Transactional
    public void openAccount(DtoUser dtoUser, String pwd, List<String> roleIds, boolean isCheckAccountExisted) {
        //先验证账号是否存在
        boolean isAccountExisted = false;
        DtoUser dbUser = userService.findByUserId(dtoUser.getId());
        if (dbUser != null) {
            isAccountExisted = true;
        }
        if (isCheckAccountExisted && isAccountExisted) {
            throw new BaseException("该用户账号已经开通过，无需重复开通");
        }
        if (!isAccountExisted) {
            //如果开通账号没有选择角色，则需要给予默认角色：通用角色
            if (StringUtils.isEmpty(roleIds)) {
                List<DtoRole> roleList = roleService.findAll();
                Optional<DtoRole> roleOptional = roleList.stream()
                        .filter(p -> IBaseConstants.RoleName.DEFAULT_ROLE_NAME.equals(p.getRoleName()) && p.getOrgId().equals(dtoUser.getOrgGuid())).findFirst();
                if (roleOptional.isPresent()) {
                    roleIds = Stream.of(roleOptional.get().getRoleId()).collect(Collectors.toList());
                } else {
                    throw new BaseException("请先在角色管理中配置默认角色: 通用角色");
                }
            }
            userService.openAccount(dtoUser, pwd, roleIds);
        }
    }

    @Override
    public Map<String, String> findOrgMap() {
        Map<String, String> result = new HashMap<>();
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        DtoUser user = userService.findByUserId(userId);
        if (IBaseConstants.OrgType.ENT.equals(user.getUserTypeCode())) {
            //企业用户返回自身所在的机构
            DtoOrg dtoOrg = orgService.findByOrgId(PrincipalContextUser.getPrincipal().getOrgId());
            result.put(dtoOrg.getId(), dtoOrg.getOrgName());
        } else {
            //其他类型用户返回全部机构
            List<DtoOrg> orgList = orgService.findList(new OrgCriteria());
            if (StringUtils.isNotEmpty(orgList)) {
                orgList.forEach(p -> result.put(p.getId(), p.getOrgName()));
            }
        }
        return result;
    }

    @Override
    public Map<String, String> findAreaMap(Collection<String> areaIds) {
        List<DtoArea> areaList = areaService.findByIds(new ArrayList<>(areaIds));
        return areaList.stream().collect(Collectors.toMap(DtoArea::getId, DtoArea::getAreaName));
    }

    @Override
    public List<DtoOrg> findAllOrg() {
        return orgService.findList(new OrgCriteria());
    }

    @Override
    public List<DeptDropdownVO> loadDepartmentDropdown() {
        DepartmentCriteria criteria = new DepartmentCriteria();
        String principalOrgType = findPrincipalOrgType();
        if (EnumOrgType.企业机构.getValue().equals(principalOrgType)) {
            criteria.setOrgTypeCode(principalOrgType);
        }
        List<DtoDepartment> departmentList = departmentService.find(criteria);
        Map<String, String> orgMap = findOrgMap();

        List<DeptDropdownVO> voList = new ArrayList<>();
        departmentList.forEach(d -> {
            DeptDropdownVO vo = new DeptDropdownVO()
                    .setDeptId(d.getId())
                    .setDeptName(d.getDeptName())
                    .setOrgId(d.getOrgGuid())
                    .setOrgName(orgMap.get(d.getOrgGuid()));
            voList.add(vo);
        });
        voList.sort(Comparator.comparing(DeptDropdownVO::getOrgName).thenComparing(DeptDropdownVO::getDeptName));
        return voList;
    }

    @Override
    public List<RoleVO> findRole(String orgId) {
        List<DtoRole> roleList;
        if (StringUtils.isNotEmpty(orgId)) {
            roleList = roleService.findRoleByOrgId(orgId);
        } else {
            roleList = roleService.findAll();
        }
        List<RoleVO> voList = new ArrayList<>();
        if (StringUtils.isNotEmpty(roleList)) {
            roleList.forEach(r -> {
                RoleVO vo = new RoleVO()
                        .setRoleId(r.getRoleId())
                        .setRoleCode(r.getRoleCode())
                        .setRoleName(r.getRoleName())
                        .setOrgId(r.getOrgId())
                        .setOrgName(r.getOrgName());
                voList.add(vo);
            });
        }
        return voList;
    }

    @Override
    public List<String> findEnabledAccountIds(List<String> personIds) {
        List<DtoUser> userList = userService.findByUserIds(personIds);
        if (StringUtils.isNotEmpty(userList)) {
            return userList.stream().map(DtoUser::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> filterPersonWithPermissionIds(List<String> personIds, List<String> permissionIds) {
        if (StringUtils.isNotEmpty(permissionIds)) {
            List<DtoUser> userList = authorizeService.findUserByAuthorize(permissionIds);
            if (StringUtils.isEmpty(userList)) {
                return Collections.EMPTY_LIST;
            } else {
                List<String> userIds = userList.stream().map(DtoUser::getId).collect(Collectors.toList());
                return personIds.stream().filter(userIds::contains).collect(Collectors.toList());
            }
        } else {
            return personIds;
        }
    }

    @Override
    public String findPrincipalOrgType() {
        DtoOrg dtoOrg = orgService.findByOrgId(PrincipalContextUser.getPrincipal().getOrgId());
        return dtoOrg.getOrgTypeCode();
    }

    /**
     * 根据父区域id加载该父区域下的子区域id，同时父区域id也会放在最后返回结果中
     *
     * @param parentIds 父区域id
     * @param allAreas  所有区域数据
     * @return 父区域id以及其所有子级id集合
     */
    private List<String> loadChildAreaIds(List<String> parentIds, List<DtoArea> allAreas) {
        List<DtoArea> childrenAreas = allAreas.stream().filter(a -> parentIds.contains(a.getParentId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(childrenAreas)) {
            List<String> childrenIds = childrenAreas.stream().map(DtoArea::getId).collect(Collectors.toList());
            parentIds.addAll(loadChildAreaIds(childrenIds, allAreas));
        }
        return parentIds;
    }

    /**
     * 获取指定编号的配置信息
     *
     * @param code 配置编号
     * @return 返回配置信息
     */
    private PathConfigVO getXmlFilePathConfigByCode(String code) {
        List<PathConfigVO> pathConfigs = getAllXmlFilePathConfig();
        Optional<PathConfigVO> optional = pathConfigs.stream().filter(p -> p.getCode().equals(code)).findFirst();
        return optional.orElse(null);
    }

    /**
     * 获取XML中所有的文件配置
     *
     * @return 结果
     */
    private List<PathConfigVO> getAllXmlFilePathConfig() {
        return filePathConfig.getAllConfigs();
    }

    @Autowired
    @Lazy
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    @Lazy
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    @Lazy
    public void setOrgService(OrgService orgService) {
        this.orgService = orgService;
    }

    @Autowired
    @Lazy
    public void setAuthorizeService(AuthorizeService authorizeService) {
        this.authorizeService = authorizeService;
    }

    @Autowired
    @Lazy
    public void setDepartmentService(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @Autowired
    @Lazy
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }

    @Autowired
    @Lazy
    public void setFilePropertyConfig(FilePropertyConfig filePropertyConfig) {
        this.filePropertyConfig = filePropertyConfig;
    }

    @Autowired
    @Lazy
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    @Lazy
    public void setConfigService(ConfigService configService) {
        this.configService = configService;
    }
}