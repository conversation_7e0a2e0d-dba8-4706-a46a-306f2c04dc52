package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.style.CellContentStyleBuilder;
import com.sinoyd.excel.style.RichTextBuilder;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.*;

/**
 * POI基于模板动态列导出
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class POIDynamicColumnTest2 {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
        FileOutputStream fos = new FileOutputStream("E:\\temp\\" + UUIDHelper.newId() + "_烟气日报表.xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
        templateExportService.exportData("/doc/template/poi/烟气日报表.xlsx", prepareData(), fos);
    }

    private List<ExcelSheetVO> prepareData() {
        Map<String, Object> infoDataMap = new HashMap<>();
        infoDataMap.put("title", "2023年7月烟气报表");
        infoDataMap.put("psName", "张家港华芳集团");
        infoDataMap.put("portName", "ZJG-HF");
        infoDataMap.put("date", "2023-08-03");

        String statement = "烟气排放总量单位：x104m3/a";
        RichTextBuilder richTextBuilder = RichTextBuilder.create(statement)
                .richSup("104", 2,2)
                .richSup("m3/", 1,1);
        statement = CellContentStyleBuilder.create(richTextBuilder.getText()).getCellValue();
        infoDataMap.put("statement", statement);

        LinkedHashMap<String, String> expandColumnMap = new LinkedHashMap<>();
        expandColumnMap.put("s34001", "氨氮");
        expandColumnMap.put("s34002", "甲苯");
        expandColumnMap.put("s34003", "乙苯");
        expandColumnMap.put("s34004", "PH");
        expandColumnMap.put("s34005", "溶解氧");
        infoDataMap.put("factorName", expandColumnMap);

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("time", "测试自适应行高测试自适应行高测试" + i);
            map.put("s34001-Avg", (int) (Math.random() * 10));
            map.put("s34001-ZsAvg", (int) (Math.random() * 10));
            map.put("s34001-Cou", (int) (Math.random() * 10));
            map.put("s34002-Avg", (int) (Math.random() * 10));
            map.put("s34002-ZsAvg", (int) (Math.random() * 10));
            map.put("s34002-Cou", (int) (Math.random() * 10));
            map.put("s34003-Avg", (int) (Math.random() * 10));
            map.put("s34003-ZsAvg", (int) (Math.random() * 10));
            map.put("s34003-Cou", (int) (Math.random() * 10));
            map.put("s34004-Avg", (int) (Math.random() * 10));
            map.put("s34004-ZsAvg", (int) (Math.random() * 10));
            map.put("s34004-Cou", (int) (Math.random() * 10));
            map.put("s34005-Avg", (int) (Math.random() * 10));
            map.put("s34005-ZsAvg", (int) (Math.random() * 10));
            map.put("s34005-Cou", (int) (Math.random() * 10));

            map.put("a00000-Cou", (int) (Math.random() * 10));
            map.put("a19001-Avg", (int) (Math.random() * 10));
            map.put("a01012-Avg", (int) (Math.random() * 10));
            map.put("a01014-Avg", (int) (Math.random() * 10));
            map.put("a90001-Avg", (int) (Math.random() * 10));


            dataList.add(map);
        }

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("烟气日报表")
                .setRenamedSheetName("烟气日报表")
                .setDataRowIdx(6)
                .setDataRowAutoHeight(true)
                .setInfoDataMap(infoDataMap)
                .setRowsDataList(dataList);
        return Collections.singletonList(excelSheetVO);
    }
}