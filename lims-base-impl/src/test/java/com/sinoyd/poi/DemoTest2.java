package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.*;

/**
 * POI基于模板导出（数据内容是实体方式）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class DemoTest2 {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
        FileOutputStream fos = new FileOutputStream("F:\\temp\\" + UUIDHelper.newId() + "_数据审核数据异常报告.xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
        templateExportService.exportData("/doc/template/poi/数据审核数据异常报告.xlsx", prepareData(), fos);
    }

    private List<ExcelSheetVO> prepareData() {
        List<ModelVO2> voList = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            ModelVO2 vo = new ModelVO2()
                    .setFactorNames("因子" + i)
                    .setExceptionDate(DateUtil.dateToString(new Date(), DateUtil.FULL))
                    .setExceptionHour(String.valueOf(i))
                    .setExceptionDesc("exceptionDesc" + i)
                    .setReason("原因" + i)
                    .setMaintainProvider("运维单位" + i)
                    .setContactMan("联系人" + i)
                    .setContactPhone("联系电话" + i)
                    .setHandlingTime(DateUtil.dateToString(new Date(), DateUtil.FULL))
                    .setHandlingResult("处理结果" + i);
            voList.add(vo);
        }
        Map<String, Object> infoDataMap = new HashMap<>();
        infoDataMap.put("reportName", "xxxx报告");
        infoDataMap.put("reporter", "张三");
        infoDataMap.put("reportPhone", "0512-66668888");
        infoDataMap.put("reportDate", DateUtil.dateToString(new Date(), DateUtil.FULL));

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("数据异常报告")
                .setRenamedSheetName("数据异常报告")
                .setDataRowIdx(2)
                .setInfoDataMap(null)
                .setPojoClass(ModelVO2.class)
                .setPojoDataList(new ArrayList<>());
        return Collections.singletonList(excelSheetVO);
    }

}