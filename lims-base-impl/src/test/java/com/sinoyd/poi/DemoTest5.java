package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.*;

/**
 * POI基于模板导出（数据内容是实体方式）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class DemoTest5 {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
        FileOutputStream fos = new FileOutputStream("E:\\temp\\" + UUIDHelper.newId() + "_污染源基本信息_行政监管.xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
        Date d1 = new Date();
        templateExportService.exportData("/doc/template/poi/污染源基本信息_行政监管.xlsx", prepareData(), fos);
        Date d2 = new Date();
        log.info("耗时：" + (d2.getTime() - d1.getTime()));
    }

    private List<ExcelSheetVO> prepareData() {
        List<PsInfoVO> data = new ArrayList<>();
        for (int i = 1; i <= 20000; i++) {
            PsInfoVO vo = new PsInfoVO();
            vo.setPsName("污染源名称" + i);
            vo.setPsCode("污染源编码" + i);
            vo.setCreditCode("统一社会信用代码" + i);
            vo.setPermitCode("排污许可证编号" + i);
            vo.setPermitManageCategoryName("排污许可证管理级别" + i);
            vo.setIndustryTypeName("行业类别" + i);
            vo.setStatIndustryTypeName("统计行业类别" + i);
            vo.setAttentionDegreeName( "关注程度" + i);
            vo.setSuperviseLevelName("监管等级" + i);
            vo.setPsTypeName("企业类型" + i);
            vo.setStatusName( "企业状态" + i);
            data.add(vo);
        }

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("污染源基本信息")
                .setDataRowIdx(1)
                .setInfoDataMap(null)
                .setPojoClass(PsInfoVO.class)
                .setIsAdjustStyle(false)
                .setPojoDataList(data);
        return Collections.singletonList(excelSheetVO);
    }

}