package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * POI基于模板导出（数据内容是实体方式）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class DemoTest4 {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
        FileOutputStream fos = new FileOutputStream("F:\\temp\\" + UUIDHelper.newId() + "_上海市气污染源在线监测数据审核报告.xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
        templateExportService.exportData("/doc/template/poi/上海市气污染源在线监测数据审核报告.xlsx", prepareData(), fos);
    }

    private List<ExcelSheetVO> prepareData() {
        Map<String,Object> info = new HashMap<>();
        info.put("psName","江苏远大");
        info.put("portName","南横套站");
        info.put("psCode","PS00001");
        info.put("portCode","PK0001");
        info.put("time","2023-12");
        info.put("cycle","12");
        info.put("factorName","PH");
        info.put("rate","70%");
        info.put("result","合格");

        List<Map<String,Object>> data = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Map<String,Object> row = new HashMap<>();
            row.put("date", "2023-12-1" + i);
            row.put("hour", "0" + i);
            row.put("factorValue", BigDecimal.valueOf(Math.random()).toPlainString());
            row.put("overProofTimes", BigDecimal.valueOf(Math.random()).toPlainString());
            data.add(row);
        }

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("报告")
                .setDataRowIdx(9)
                .setInfoDataMap(info)
                .setRowsDataList(data);
        return Collections.singletonList(excelSheetVO);
    }

}