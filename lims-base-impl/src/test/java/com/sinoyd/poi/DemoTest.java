package com.sinoyd.poi;

import com.sinoyd.ApplicationTestRunner;
import com.sinoyd.excel.service.ITemplateExportService;
import com.sinoyd.excel.vo.ExcelSheetVO;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * POI基于模板导出（数据内容是实体方式）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/12/13
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApplicationTestRunner.class)
@Slf4j
public class DemoTest {

    @Autowired
    private ITemplateExportService templateExportService;

    @Test
    public void dynamicColumnTest() throws FileNotFoundException {
//        FileOutputStream fos = new FileOutputStream("F:\\temp\\" + UUIDHelper.newId() + "_在线监测数据审核报告(日).xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
//        templateExportService.exportData("/doc/template/poi/在线监测数据审核报告(日).xlsx", prepareData(), fos);

        FileOutputStream fos = new FileOutputStream("E:\\temp\\" + UUIDHelper.newId() + "_在线监测数据审核报告(日).xlsx");
//        FileOutputStream fos = new FileOutputStream(this.getClass().getResource("/temp").getPath() + UUIDHelper.newId() + "_DynamicColumn.xlsx");
        templateExportService.exportData("/doc/template/poi/在线监测数据审核报告(日).xlsx", prepareData(10), fos);
    }

    private List<ExcelSheetVO> prepareData(int x) {
        List<ExcelSheetVO> excelSheetVOList = new ArrayList<>();
        Map<String, Object> infoDataMap = new HashMap<>();
        infoDataMap.put("psName", "江苏远大");
        infoDataMap.put("portName", "沙洲湖");
        infoDataMap.put("psCode", "# #032603001");
        infoDataMap.put("portCode", "ZJG-SZH-01");
        infoDataMap.put("tstamp", "2024-01-01");
        infoDataMap.put("remark", "结论结论结论结论结论结论结论结论结论");

        List<Map<String, Object>> rowDataList = new ArrayList<>();
        for (int i = 1; i <= x; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("factorName", "氨氮" + i);
            map.put("upperValue", 10 * i);
//            map.put("factorValue", 2 * i);
            map.put("factorValue", new BigDecimal(0));
            rowDataList.add(map);
        }

//        List<MergeVO> mergeVOList = new ArrayList<>();

//        mergeVOList.add(new MergeVO(8, 9, 0, 0));
//        mergeVOList.add(new MergeVO(8, 9, 4, 4));

        ExcelSheetVO excelSheetVO = new ExcelSheetVO()
                .setSheetName("报告")
                .setDataRowIdx(8)
//                .setMergeList(mergeVOList)
                .setInfoDataMap(infoDataMap)
                .setRowsDataList(rowDataList);
        excelSheetVOList.add(excelSheetVO);
        return excelSheetVOList;
    }

}