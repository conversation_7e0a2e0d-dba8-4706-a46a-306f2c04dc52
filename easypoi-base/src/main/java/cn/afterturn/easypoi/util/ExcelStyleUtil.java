package cn.afterturn.easypoi.util;

import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;

/**
 * Excel 样式工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/14
 */
public class ExcelStyleUtil {

    public static final short FONT_SIZE_TEN = 10;
    public static final short FONT_SIZE_ELEVEN = 11;
    public static final short FONT_SIZE_TWELVE = 12;

    /**
     * 获取表格标题样式
     *
     * @param workbook 工作表
     * @return 样式
     */
    public static CellStyle getTitleStyle(Workbook workbook) {
        CellStyle style = getBaseCellStyle(workbook);
        style.setFont(getFont(workbook, FONT_SIZE_TWELVE, true));
        return style;
    }

    /**
     * 获取表格头部样式
     *
     * @param workbook 工作表
     * @return 样式
     */
    public static CellStyle getHeaderStyle(Workbook workbook) {
        CellStyle style = getBaseCellStyle(workbook);
        style.setFont(getFont(workbook, FONT_SIZE_ELEVEN, true));
        //水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        //上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 获取表格数据头部样式
     *
     * @param workbook 工作表
     * @return 样式
     */
    public static CellStyle getDataHeaderStyle(Workbook workbook) {
        CellStyle style = getBaseCellStyle(workbook);
        style.setFont(getFont(workbook, FONT_SIZE_ELEVEN, true));
        //水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        //上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    /**
     * 获取表格数据部分样式
     *
     * @param workbook 工作表
     * @return 样式
     */
    public static CellStyle getDataStyle(Workbook workbook) {
        CellStyle style = getBaseCellStyle(workbook);
        style.setFont(getFont(workbook, FONT_SIZE_TEN, false));
        //水平居中
        style.setAlignment(HorizontalAlignment.LEFT);
        //上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 获取数据区域样式
     *
     * @param workbook  工作表
     * @param fontColor 字体颜色
     * @return 样式
     */
    public static CellStyle getDataAreaStyle(Workbook workbook, short fontColor) {
        return getDataAreaStyle(workbook, fontColor, true);
    }

    /**
     * 获取数据区域样式
     *
     * @param workbook  工作表
     * @param fontColor 字体颜色
     * @param isBlob    字体是否加粗
     * @return 样式
     */
    public static CellStyle getDataAreaStyle(Workbook workbook, short fontColor, boolean isBlob) {
        CellStyle style = getBaseCellStyle(workbook);
        Font font = getFont(workbook, FONT_SIZE_TEN, isBlob);
        font.setColor(fontColor);
        style.setFont(font);
        return style;
    }

    public static HSSFColor getColor(String color) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFPalette palette = workbook.getCustomPalette();
        String r = color.substring(1, 3);
        String g = color.substring(3, 5);
        String b = color.substring(5, 7);
        int r2 = Integer.parseInt(r, 16);
        int g2 = Integer.parseInt(g, 16);
        int b2 = Integer.parseInt(b, 16);
        return palette.findSimilarColor(r2, g2, b2);
    }

    /**
     * 基础样式
     *
     * @param workbook 工作簿
     * @return 单元格样式
     */
    public static CellStyle getBaseCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        //下边框
        style.setBorderBottom(BorderStyle.THIN);
        //左边框
        style.setBorderLeft(BorderStyle.THIN);
        //上边框
        style.setBorderTop(BorderStyle.THIN);
        //右边框
        style.setBorderRight(BorderStyle.THIN);
        //水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        //上下居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置自动换行
        style.setWrapText(true);
        return style;
    }

    /**
     * 字体样式
     *
     * @param size   字体大小
     * @param isBold 是否加粗
     * @return 字体
     */
    public static Font getFont(Workbook workbook, short size, boolean isBold) {
        Font font = workbook.createFont();
        //字体样式
        font.setFontName("宋体");
        //是否加粗
        font.setBold(isBold);
        //字体大小
        font.setFontHeightInPoints(size);
        return font;
    }

    /**
     * 字体样式
     *
     * @param workbook 工作簿对象
     * @param size     字体大小
     * @param isBlob   是否加粗
     * @param color    颜色，十六进制RGB形式
     * @return 字体
     */
    public static Font getFont(Workbook workbook, short size, boolean isBlob, String color) {
        Font font = workbook.createFont();
        //字体样式
        font.setFontName("宋体");
        //是否加粗
        font.setBold(isBlob);
        //字体大小
        font.setFontHeightInPoints(size);
        if(color != null && !"".equals(color)){
            font.setColor(ExcelStyleUtil.getColor(color).getIndex());
        }

        return font;
    }

    /**
     * 字体样式
     *
     * @param workbook 工作簿对象
     * @param size     字体大小
     * @param isBlob   是否加粗
     * @param color    颜色，十六进制RGB形式
     * @return 字体
     */
    public static Font getFont(Workbook workbook, short size, boolean isBlob, short color) {
        Font font = workbook.createFont();
        //字体样式
        font.setFontName("宋体");
        //是否加粗
        font.setBold(isBlob);
        //字体大小
        font.setFontHeightInPoints(size);
        font.setColor(color);
        return font;
    }
}