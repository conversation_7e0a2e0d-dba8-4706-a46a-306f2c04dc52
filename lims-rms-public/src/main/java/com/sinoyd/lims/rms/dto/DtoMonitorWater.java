package com.sinoyd.lims.rms.dto;

import com.sinoyd.base.vo.TreeNodeVO;
import com.sinoyd.lims.rms.entity.MonitorWater;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 水体传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/4
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorWater")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoMonitorWater extends MonitorWater {

    /**
     * 水体类型名称
     */
    @Transient
    private String typeName;

    /**
     * 父级id
     */
    @Transient
    private String parentName;

    /**
     * 获取树节点
     *
     * @return 树节点
     */
    public TreeNodeVO loadTreeNodeVO() {
        return new TreeNodeVO()
                .setId(getId())
                .setLabel(getName())
                .setOrderNum(getOrderNum())
                .setParentId(getParentId())
                .setExtent1(getCode())
                .setExtent2(getTypeCode());
    }

}
