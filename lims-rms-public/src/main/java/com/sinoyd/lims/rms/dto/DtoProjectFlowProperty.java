package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.ProjectFlowProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 流程性质传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/6/19
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_ProjectFlowProperty")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
@Accessors(chain = true)
public class DtoProjectFlowProperty extends ProjectFlowProperty {

    /**
     * 工作流名称
     */
    @Transient
    private String workflowName;
}