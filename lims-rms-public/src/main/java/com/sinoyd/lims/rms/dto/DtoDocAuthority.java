package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.DocAuthority;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;


/**
 * 文件夹权限传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_DocAuthority")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoDocAuthority extends DocAuthority {

}