package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.RmsMonitorPlanPoint2Test;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 监测计划点位测试项目关联传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/30
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorPlanPoint2Test")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsMonitorPlanPoint2Test extends RmsMonitorPlanPoint2Test {
    /**
     * 分析因子id
     */
    @Transient
    private String analyzeItemId;

    /**
     * 分析因子名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 分析方法id
     */
    @Transient
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    @Transient
    private String analyzeMethodName;

    /**
     * 分析方法编号
     */
    @Transient
    private String methodStandard;

    /**
     * 评价标准名称
     */
    @Transient
    private String evaluateCriteriaName;

    /**
     * 评价标准等级名称
     */
    @Transient
    private String evaluateLevelName;

    /**
     * 量纲名称
     */
    @Transient
    private String dimensionName;

    /**
     * 该测试项目的监测类型大类id
     */
    @Transient
    private String sampleTypeId;

    /**
     *  父测试项目标识
     */
    @Transient
    private String parentTestId;

    /**
     *  评价限值id
     */
    @Transient
    private String evaluationValueId;

    /**
     * 总称测试项目子项
     */
    @Transient
    private List<DtoRmsMonitorPlanPoint2Test> children;

    /**
     * 清空评价标准
     */
    public void cleatEvaluate(){
        this.setEvaluationCriteriaId("")
                .setEvaluationLevelId("")
                .setLowerLimit("")
                .setLowerSymbol("")
                .setUpperLimit("")
                .setUpperSymbol("");
    }
}