package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.ReagentConfigMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 试剂配制记录与分析方法关联传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/21
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_ReagentConfigMethod")
@Data
@Accessors(chain = true)
@Where(clause = "isDeleted = 0")
public class DtoReagentConfigMethod extends ReagentConfigMethod {
}