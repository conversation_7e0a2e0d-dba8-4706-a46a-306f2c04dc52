package com.sinoyd.lims.rms.dto;

import com.sinoyd.base.vo.TreeNodeVO;
import com.sinoyd.lims.rms.entity.IndustryType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 行业类别传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_IndustryType")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoIndustryType extends IndustryType {

    /**
     * 将行业类型实体转换成树结构
     * <p>其中，id为行业类型id， label为行业类型名称，type固定为行业类型，供前端进行标识</p>
     *
     * @return 行业类型树
     */
    public TreeNodeVO loadTreeNodeVO() {
        return new TreeNodeVO()
                .setId(getId())
                .setLabel(getIndustryName())
                .setType("行业类型")
                .setOrderNum(getOrderNum());
    }
}