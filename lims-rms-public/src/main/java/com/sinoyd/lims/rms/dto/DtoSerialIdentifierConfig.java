package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.SerialIdentifierConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * 编号配置传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/18
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_SerialIdentifierConfig")
@Data
@DynamicInsert
@Accessors(chain = true)
@Where(clause = "isDeleted = 0")
public class DtoSerialIdentifierConfig extends SerialIdentifierConfig {
    private static final long serialVersionUID = 1L;

    /**
     * 配置规则详情
     */
    @Transient
    private List<DtoSerialRuleDetail> ruleDetailList;

    /**
     * 是否显示项目类型
     */
    @Transient
    private Boolean isShowProjectType = false;

    /**
     * 是否显示质控样信息
     */
    @Transient
    private Boolean isShowQcInfo = false;

}