package com.sinoyd.lims.rms.dto;

import com.sinoyd.base.vo.TreeNodeVO;
import com.sinoyd.lims.rms.entity.EvaluationLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;


/**
 * 评价等级传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_EvaluationLevel")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoEvaluationLevel extends EvaluationLevel {
    private static final long serialVersionUID = 1L;

    /**
     * 评价标准名称全称(用于评价标准导入)
     * 例如：
     * 一级数据下有二级数据，
     * 则全称为 [一级评价等级名称] + [导入拼接占位符] + [二级评价等级名称]
     */
    @Transient
    private String fullName;

    /**
     * 评价值
     */
    @Transient
    private List<DtoEvaluationValue> evaluationValue = new ArrayList<>();

    /**
     * 将评价等级实例转换为树节点实例
     *
     * @return 树节点实例
     */
    @Transient
    public TreeNodeVO getTreeNode() {
        TreeNodeVO treeNode = new TreeNodeVO();
        treeNode.setId(getId());
        treeNode.setParentId(getParentId());
        treeNode.setLabel(getName());
        treeNode.setOrderNum(getOrderNum());
        treeNode.setType("evaluationLevel");
        treeNode.setIsLeaf(Boolean.FALSE);
        treeNode.setChildren(new ArrayList<>());
        return treeNode;
    }
}