package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.MonitorPointSort;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 监测点位排序传输对象
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/3
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorPointSort")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoMonitorPointSort extends MonitorPointSort {

    /**
     * 监测点位排序详情列表
     */
    @Transient
    private List<DtoMonitorPointSortDetail> detailList;

}
