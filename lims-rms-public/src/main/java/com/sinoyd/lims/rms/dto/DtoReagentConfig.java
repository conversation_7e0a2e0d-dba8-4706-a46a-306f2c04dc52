package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.ReagentConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 标准曲线传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_ReagentConfig")
@Data
@DynamicInsert
@Accessors(chain = true)
@Where(clause = "isDeleted = 0")
public class DtoReagentConfig extends ReagentConfig {

    /**
     * 配置人员名称
     */
    @Transient
    private String configPersonName;

    /**
     * 适用方法名称
     */
    @Transient
    private String methodNames;

    /**
     * 关联原始记录单名称
     */
    @Transient
    private String workSheetNames;

    /**
     * 使用方法数据
     */
    @Transient
    private List<DtoReagentConfigMethod> configMethods;
}
