package com.sinoyd.lims.rms.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * 车辆管理实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/02/09
 */
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class CarManage extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public CarManage() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 车牌号码
     */
    private String carCode;

    /**
     * 车辆型号
     */
    private String carModel;

    /**
     * 车辆类型（常量lims_rms_carType:轿车、货车、商务车、SUV）
     */
    private String carType;

    /**
     * 车辆状态(枚举EnumCarState:1:正常 2:维修,3:停用;4:过期)
     */
    private Integer state;

    /**
     * 负责人Id（Guid）
     */
    private String managerId;

    /**
     * 购置日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date buyDate;

    /**
     * 发动机号码
     */
    private String engineCode;

    /**
     * 最近年检日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date annualReviewDate;

    /**
     * 年检周期（月）
     */
    private Integer annualReviewCycle;

    /**
     * 油耗
     */
    private String oilConsumption;

    /**
     * 备注
     */
    private String remark;

    /**
     * GPS型号
     */
    private String gpsModelNo;

    /**
     * GPS编号
     */
    private String gpsSerialNo;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}