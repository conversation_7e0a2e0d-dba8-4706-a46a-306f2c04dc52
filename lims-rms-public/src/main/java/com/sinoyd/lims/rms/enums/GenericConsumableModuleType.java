package com.sinoyd.lims.rms.enums;

import com.sinoyd.lims.rms.constant.LIMCodeConstants;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消耗品标样类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum GenericConsumableModuleType {
    CONSUMABLE(LIMCodeConstants.CodeType.CONSUMABLE_LEVEL,
            LIMCodeConstants.CodeType.CONSUMABLE_CATEGORY),
    STANDARD(LIMCodeConstants.CodeType.STANDARD_LEVEL,
            LIMCodeConstants.CodeType.STANDARD_CATEGORY);
    /**
     * 等级常量编码
     */
    private String levelCode;
    /**
     * 类型常量编码
     */
    private String category;
}