package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.MonitorStation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 测站传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/1/3
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorStation")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoMonitorStation extends MonitorStation {

    /**
     * 部门名
     */
    @Transient
    private String departmentName;

    /**
     * 区域名
     */
    @Transient
    private String areaName;
}
