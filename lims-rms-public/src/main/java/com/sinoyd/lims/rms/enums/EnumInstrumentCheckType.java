package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仪器鉴定类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumInstrumentCheckType {
    送检(1),
    上门(2);
    private int value;

    /**
     * 根据值获取名称
     *
     * @param value 常量值
     * @return 名称
     */
    public static String getNameByValue(int value) {
        String result = "";
        for (EnumInstrumentCheckType enumInstrumentCheckType : EnumInstrumentCheckType.values()) {
            if (enumInstrumentCheckType.value == value) {
                result = enumInstrumentCheckType.name();
                break;
            }
        }
        return result;
    }
}