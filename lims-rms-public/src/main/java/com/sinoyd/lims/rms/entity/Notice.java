package com.sinoyd.lims.rms.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;


/**
 * 公告实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/01/15
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Notice extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public Notice() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 公告标题
     */
    private String title;

    /**
     * 公告类型（常量）：LIM_NoticeCategory(1通知、2行政、4标准规范、8内部管理、16其他)
     */
    private String category;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 是否发布（0.不发布 1.发布）
     */
    private Boolean isRelease;

    /**
     * 发布人id
     */
    private String releaseId;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 是否置顶(0: 不置顶, 1: 置顶)
     */
    private Boolean isTop;

    /**
     * 公告标签（常量）：LIM_NoticeLabel（一般、紧急、重要）
     */
    private String label;

    /**
     * 浏览次数
     */
    private Integer clickNumber;

    /**
     * 假删标识
     */
    private Boolean isDeleted;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}