package com.sinoyd.lims.rms.dto;

import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.lims.rms.entity.PersonCertificateAbility;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 检测能力传输dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/24
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_PersonCertificateAbility")
@Data
@DynamicInsert
@Accessors(chain = true)
@GlobalExport(name = "上岗证明细")
public class DtoPersonCertificateAbility extends PersonCertificateAbility {

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 分析方法名称
     */
    @Transient
    private String analyzeMethodName;

    /**
     * 分析方法标准编号
     */
    @Transient
    private String standardNo;

    /**
     * 检测类型id
     */
    @Transient
    private String sampleTypeId;

    /**
     * 检测类型上的图标
     */
    @Transient
    private String sampleTypeIcon;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 人员证书
     */
    @Transient
    private String personCertCode;

    /**
     * 证书名称
     */
    @Transient
    private String personCertName;

    /**
     * 人员Id
     */
    @Transient
    private String personId;

    /**
     * 批量操作的id列表
     */
    @Transient
    private List<String> operationIds = new ArrayList<>();

    /**
     * 测试项目id传输数组
     */
    @Transient
    private List<String> testIds = new ArrayList<>();

    public DtoPersonCertificateAbility() {
    }

    /**
     * 分页查询构造方法
     *
     * @param id                主键
     * @param analyzeItemName   分析项目名称
     * @param analyzeMethodName 分析方法名称
     * @param standardNo        标准编号
     * @param personCertCode    证书编号
     * @param achieveDate       发证日期
     * @param certEffectiveTime 有效期至
     */
    public DtoPersonCertificateAbility(String id,
                                       String analyzeItemName,
                                       String analyzeMethodName,
                                       String standardNo,
                                       String personCertCode,
                                       String sampleTypeId,
                                       Date achieveDate,
                                       Date certEffectiveTime,
                                       String testId,
                                       String personCertId) {
        setId(id);
        this.analyzeItemName = analyzeItemName;
        this.analyzeMethodName = analyzeMethodName;
        this.standardNo = standardNo;
        this.personCertCode = personCertCode;
        this.sampleTypeId = sampleTypeId;
        setAchieveDate(achieveDate);
        setCertEffectiveTime(certEffectiveTime);
        setTestId(testId);
        setPersonCertId(personCertId);
    }

    /**
     * 构造方法
     *
     * @param id                主键
     * @param testId            测试项目id
     * @param personCertId      证书id
     * @param analyzeItemName   分析项目名称
     * @param analyzeMethodName 分析方法名称
     * @param standardNo        标准编号
     * @param sampleTypeName    检测类型名称
     * @param personCertCode    证书编号
     * @param achieveDate       发证日期
     * @param certEffectiveTime 有效期至
     */
    public DtoPersonCertificateAbility(String id,
                                       String testId,
                                       String personCertId,
                                       String analyzeItemName,
                                       String analyzeMethodName,
                                       String standardNo,
                                       String sampleTypeName,
                                       String personCertCode,
                                       Date achieveDate,
                                       Date certEffectiveTime,
                                       String personId) {
        setId(id);
        setTestId(testId);
        setPersonCertId(personCertId);
        this.analyzeItemName = analyzeItemName;
        this.analyzeMethodName = analyzeMethodName;
        this.standardNo = standardNo;
        this.personCertCode = personCertCode;
        this.sampleTypeName = sampleTypeName;
        setAchieveDate(achieveDate);
        setCertEffectiveTime(certEffectiveTime);
        this.personId = personId;
    }

    /**
     * 根据测试项目批量创建检测能力对象
     *
     * @return 完成创建的检测能力对象列表
     */
    public List<DtoPersonCertificateAbility> initByTests() {
        List<DtoPersonCertificateAbility> result = new ArrayList<>();
        List<String> testIds = getTestIds();
        for (String testId : testIds) {
            DtoPersonCertificateAbility ability = new DtoPersonCertificateAbility();
            BeanUtils.copyProperties(this, ability, "id", "testIds");
            ability.setTestId(testId);
            result.add(ability);
        }
        return result;
    }
}