package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentStorageRecordDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 仪器出入库记录明细传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentStorageRecordDetail")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentStorageRecordDetail extends InstrumentStorageRecordDetail {

    /**
     * 仪器列表，用于新增出库记录时传递数据
     */
    @Transient
    private List<DtoInstrument> instruments = new ArrayList<>();

    /**
     * 入库的明细id
     */
    @Transient
    private List<String> detailIds = new ArrayList<>();

    /**
     * 出入库记录日期
     */
    @Transient
    private Date recordDate;

    /**
     * 任务id
     */
    @Transient
    private String projectId;

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 使用人姓名
     */
    @Transient
    private String userNames;

    /**
     * 管理员id
     */
    @Transient
    private String manager;

    /**
     * 管理员姓名
     */
    @Transient
    private String managerName;

    /**
     * 默认构造方法
     */
    public DtoInstrumentStorageRecordDetail() {
    }

    /**
     * 构造方法
     *
     * @param id         主键
     * @param recordDate 出入库记录日期
     * @param projectId  项目id
     * @param outStatus  出库情况
     * @param backStatus 入库情况
     * @param userNames  使用人姓名
     * @param manager    管理员id
     */
    public DtoInstrumentStorageRecordDetail(String id,
                                            Date recordDate,
                                            String projectId,
                                            Boolean outStatus,
                                            Boolean backStatus,
                                            String userNames,
                                            String manager) {
        setId(id);
        setRecordDate(recordDate);
        setProjectId(projectId);
        setOutStatus(outStatus);
        setBackStatus(backStatus);
        setUserNames(userNames);
        setManager(manager);
    }

}
