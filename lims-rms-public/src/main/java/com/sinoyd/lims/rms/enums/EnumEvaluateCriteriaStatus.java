package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评价标准状态
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumEvaluateCriteriaStatus {
    全部(-1),
    有效(1),
    废止(2);
    private int value;

    public static String getNameByValue(int value) {
        String result = "";
        for (EnumEvaluateCriteriaStatus status : EnumEvaluateCriteriaStatus.values()) {
            if (status.value == value) {
                result = status.name();
                break;
            }
        }
        return result;
    }
}
