package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentUseRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;


/**
 * 仪器使用记录传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentUseRecord")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentUseRecord extends InstrumentUseRecord {

    /**
     * 关联样品的id列表
     */
    @Transient
    private List<String> sampleIds = new ArrayList<>();

    /**
     * 仪器id集合（用于新增仪器使用记录时传参）
     */
    @Transient
    private List<String> instrumentIds;

    /**
     * 所选测试项目id集合（用于新增仪器使用记录时传参）
     */
    @Transient
    private List<String> testIds;

    /**
     * 使用记录与样品关联数据id
     */
    @Transient
    private List<String> useRecordSampleIds = new ArrayList<>();

    /**
     * 使用记录下的关联样品数据
     */
    @Transient
    private List<DtoInstrumentUseRecord2Sample> instrumentUseRecord2Samples = new ArrayList<>();

    /**
     * 使用记录下的关联仪器数据
     */
    @Transient
    private List<DtoInstrumentUseRecord2Instrument> instrumentUseRecord2Instruments = new ArrayList<>();

    /**
     * 使用记录下关联的测试项目数据
     */
    @Transient
    private List<DtoInstrumentUseRecord2Test> instrumentUseRecord2Tests = new ArrayList<>();
    /**
     * 使用记录id
     */
    @Transient
    private String useRecordId;

    /**
     * 使用者姓名
     */
    @Transient
    private String usePersonName;

    /**
     * 测试项目名称，多个使用英文逗号隔开
     */
    @Transient
    private String testNames;

    /**
     * 所在实验室名称
     */
    @Transient
    private String labName;

    /**
     * 测试项目名称集合
     */
    @Transient
    private List<String> testNameList = new ArrayList<>();

}