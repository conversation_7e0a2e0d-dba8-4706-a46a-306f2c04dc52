package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.AnalyzeMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 分析方法传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_AnalyzeMethod")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoAnalyzeMethod extends AnalyzeMethod {

    /**
     * 被替换的分析方法id
     */
    @Transient
    private String oldMethodId;

    /**
     * 默认构造方法
     */
    public DtoAnalyzeMethod() {
    }

    /**
     * 构造方法
     *
     * @param analyzeMethodName 分析方法名称
     * @param standardNo   标准编号
     */
    public DtoAnalyzeMethod(String analyzeMethodName, String standardNo) {
        setMethodName(analyzeMethodName);
        setStandardNo(standardNo);
    }
}