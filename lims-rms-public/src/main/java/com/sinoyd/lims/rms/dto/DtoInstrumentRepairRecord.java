package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentRepairRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 仪器维修记录传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentRepairRecord")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentRepairRecord extends InstrumentRepairRecord {

    /**
     * 验证人名称
     */
    @Transient
    private String checker;

    /**
     * 描述人名称
     */
    @Transient
    private String failureDescPerson;

    /**
     * 记录人名称
     */
    @Transient
    private String recorder;

}