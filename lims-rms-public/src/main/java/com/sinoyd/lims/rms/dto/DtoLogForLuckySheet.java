package com.sinoyd.lims.rms.dto;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.entity.CurrentPrincipalUser;
import com.sinoyd.lims.rms.entity.LogForLuckySheet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 在线编辑日志表传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/28
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_LogForLuckySheet")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
@Accessors(chain = true)
public class DtoLogForLuckySheet extends LogForLuckySheet {

    /**
     * 当前日志修改的单元格明细
     */
    @Transient
    private List<DtoLogForLuckySheetDetail> detailList;

    /**
     * 无参构造函数
     */
    public DtoLogForLuckySheet() {
        super();
    }

    /**
     * 有参构造函数（初始化实体对象）
     *
     * @param objectId    日志对象id
     * @param operateInfo 操作信息
     * @param context     操作内容
     */
    public DtoLogForLuckySheet(String objectId, String operateInfo, String context) {
        this();
        CurrentPrincipalUser principalUser = PrincipalContextUser.getPrincipal();
        setObjectId(objectId);
        setOperatorId(principalUser.getUserId());
        setOperatorName(principalUser.getUserName());
        setOperateTime(new Date());
        setOperateInfo(operateInfo);
        setContent(context);
    }
}