package com.sinoyd.lims.rms.dto;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.lims.rms.entity.InstrumentCheckRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 仪器检定校准传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentCheckRecord")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentCheckRecord extends InstrumentCheckRecord {

    /**
     * 仪器id
     */
    @Transient
    private List<String> instrumentIds;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 规格型号
     */
    @Transient
    private String instrumentModel;

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentCode;

    /**
     * 检定校准人员
     */
    @Transient
    private String checkPerson;

    /**
     * 检定
     */
    @Transient
    private String originEndDateString;

    /**
     * 溯源方式
     */
    @Transient
    private String originTypeName;

    public DtoInstrumentCheckRecord() {
    }

    /**
     * 分页查询构造方法
     */
    public DtoInstrumentCheckRecord(String id, String instrumentId, String originTypeCode, String checkDeptName,
                                    Date checkTime, Boolean checkResult, BigDecimal originCyc, String certificateCode,
                                    String checkPersonId,
                                    String instrumentName, String instrumentModel, String instrumentCode) {
        setId(id);
        setInstrumentId(instrumentId);
        setOriginCyc(originCyc);
        setCheckDeptName(checkDeptName);
        setOriginTypeCode(originTypeCode);
        setCheckTime(checkTime);
        setCheckResult(checkResult);
        setCertificateCode(certificateCode);
        setCheckPersonId(checkPersonId);
        setInstrumentName(instrumentName);
        setInstrumentModel(instrumentModel);
        setInstrumentCode(instrumentCode);
        setOriginEndDateString(DateUtil.dateToString(DateUtil.dateAddMonths(checkTime, originCyc.intValue()), DateUtil.YEAR));
    }
}