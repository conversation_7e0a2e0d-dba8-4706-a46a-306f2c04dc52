package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentOutPurpose;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 仪器出库用途传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/6/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentOutPurpose")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentOutPurpose extends InstrumentOutPurpose {

    /**
     * 项目名称
     */
    @Transient
    private String projectName;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 项目业务人员名称（项目负责人名称）
     */
    @Transient
    private String merchandiserName;
}