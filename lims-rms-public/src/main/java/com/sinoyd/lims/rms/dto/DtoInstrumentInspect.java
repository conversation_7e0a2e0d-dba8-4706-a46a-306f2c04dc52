package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentInspect;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;


/**
 * 仪器期间核查传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/04
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentInspect")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentInspect extends InstrumentInspect {

    /**
     * 核查人员名称
     */
    @Transient
    private String inspectPerson;

}