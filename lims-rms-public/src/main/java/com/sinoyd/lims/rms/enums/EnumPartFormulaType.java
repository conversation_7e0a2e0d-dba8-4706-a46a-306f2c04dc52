package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公式类型枚举
 *  0.修约公式（用于测试公式及原始记录单参数公式中的修约）
 *  1.检测类型参数公式
 *  2.加标公式
 *  3.BOD5判断公式
 *  4.参数公式（如减空白后吸光度=吸光度-空白）
 *  5.串联出证公式）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumPartFormulaType {
    修约公式(0, "revise"),
    检测类型参数公式(1, "sampleType"),
    加标公式(2, "standard"),
    BOD5公式(3, "bod5"),
    参数公式(4, "param"),
    串联公式(5, "cascade"),
    折算公式(6, "convert"),
    替代公式(7, "replace"),
    测得量公式(8, "measured");
    private Integer value;
    private String name;

    public static int getValueByName(String name) {
        int result = -1;
        for (EnumPartFormulaType enumPartFormulaType : EnumPartFormulaType.values()) {
            if (enumPartFormulaType.name.equals(name)) {
                result = enumPartFormulaType.value;
            }
        }
        return result;
    }

    public static String getNameByValue(Integer value) {
        String result = "";
        for (EnumPartFormulaType enumPartFormulaType : EnumPartFormulaType.values()) {
            if (enumPartFormulaType.value.equals(value)) {
                result = enumPartFormulaType.name;
            }
        }
        return result;
    }
}