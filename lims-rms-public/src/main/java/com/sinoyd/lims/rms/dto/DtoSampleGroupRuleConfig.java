package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.SampleGroupRuleConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * 样品分组传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_SampleGroupRuleConfig")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoSampleGroupRuleConfig extends SampleGroupRuleConfig {

    /**
     * 测试项目id，用于保存关联测试项目
     */
    @Transient
    private List<String> testIds;

    /**
     * 测试项目名称，用于前端列表展示，多个测试项目名称使用英文逗号隔开
     */
    @Transient
    private String testNames;

    /**
     * 分组下的测试项目数量
     */
    @Transient
    private int testCount;

    /**
     * 关联的测试项目数据
     */
    @Transient
    private List<DtoSampleGroupRuleConfig2Test> sampleTypeGroup2TestList;

}