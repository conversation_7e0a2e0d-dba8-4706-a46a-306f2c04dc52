package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分析方法状态
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumAnalyzeMethodStatus {
    启用(1),
    停用(2),
    废止(3);
    private Integer value;

    public static String getNameByValue(Integer value) {
        String result = "";
        for (EnumAnalyzeMethodStatus enumAnalyzeMethodStatus : EnumAnalyzeMethodStatus.values()) {
            if (enumAnalyzeMethodStatus.value.equals(value)) {
                result = enumAnalyzeMethodStatus.name();
            }
        }
        return result;
    }
}
