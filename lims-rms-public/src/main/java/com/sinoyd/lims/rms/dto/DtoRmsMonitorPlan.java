package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.RmsMonitorPlan;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 监测计划传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/30
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorPlan")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsMonitorPlan extends RmsMonitorPlan {
    /**
     * 复制源id
     */
    @Transient
    private String sourceId;
}