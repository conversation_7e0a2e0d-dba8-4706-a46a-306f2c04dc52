package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.AnalyzeItemSortDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 分析项目排序详情传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_AnalyzeItemSortDetail")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoAnalyzeItemSortDetail extends AnalyzeItemSortDetail {

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;
}