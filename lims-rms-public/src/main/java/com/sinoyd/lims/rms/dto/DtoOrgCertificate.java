package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.OrgCertificate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 机构证书传输dto
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/22
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_OrgCertificate")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoOrgCertificate extends OrgCertificate {

    /**
     * 证书类型名称
     */
    @Transient
    private String typeName;

    /**
     * 管理员姓名
     */
    @Transient
    private String managerName;

    /**
     * 状态名称
     */
    @Transient
    private String statusName;

}
