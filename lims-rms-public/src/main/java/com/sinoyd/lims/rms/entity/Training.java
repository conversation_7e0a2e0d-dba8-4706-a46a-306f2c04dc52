package com.sinoyd.lims.rms.entity;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 培训管理实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/21
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class Training extends LimsBaseEntity {
    private static final long serialVersionUID = 1L;

    public Training() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 流水号配置id
     */
    private String trainingName;

    /**
     * 培训时间
     */
    private Date trainingDate;

    /**
     * 培训方式，常量lims_rms_trainingType编码
     */
    private String trainingTypeCode;

    /**
     * 培训时长
     */
    private BigDecimal trainingDuration;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 培训讲师
     */
    private String trainer;

    /**
     * 内容
     */
    private String trainingContent;

    /**
     * 参与人，多个使用英文逗号隔开
     */
    private String participants;

    /**
     * 组织机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 所属实验室
     */
    private String domainId;

    /**
     * 修改人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 修改时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
