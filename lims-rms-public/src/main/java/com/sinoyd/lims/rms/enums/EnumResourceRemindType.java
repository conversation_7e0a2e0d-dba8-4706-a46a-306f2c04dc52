package com.sinoyd.lims.rms.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 首页资源提醒统计类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/02/10
 */
@Getter
@AllArgsConstructor
public enum EnumResourceRemindType {

    耗材过期提醒("ConsumableOverdue", "consumableOverdueRemind"),

    耗材低库存提醒("ConsumableLowerStorage", "consumableLowerStorageRemind"),

    仪器过期提醒("InstrumentOverdue", "instrumentOverdueRemind"),

    上岗证过期提醒("CertificateOverdue", "certificateOverdueRemind");

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 统计策略类名
     */
    private final String beanName;

    /**
     * 根据编码获取枚举项
     *
     * @param code 枚举编码
     * @return 枚举项
     */
    public static EnumResourceRemindType getEnumByCode(String code) {
        for (EnumResourceRemindType e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BaseException(String.format("非法的首页资源提醒统计类型枚举编码[%s]", code));
    }
}
