package com.sinoyd.lims.rms.enums;

/**
 * 参数对象类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
public enum EnumParamConfigType {
    检测类型参数(1),
    分析项目参数(2),
    企业参数(3),
    方法参数(4),
    采样单参数(5),
    原始记录单数据参数(6),
    报告参数(7),
    采样单分组样品参数(8),
    原始记录单表头参数(9),
    测试项目公式参数(10);
    private Integer value;

    EnumParamConfigType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
