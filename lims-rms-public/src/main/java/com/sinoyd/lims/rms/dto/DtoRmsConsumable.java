package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.Consumable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 消耗品传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_Consumable")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsConsumable extends Consumable {

    /**
     * 类别名称
     */
    @Transient
    private String typeName;

    /**
     * 等级名称
     */
    @Transient
    private String levelName;

    /**
     * 单位名称
     */
    @Transient
    private String unitName;

    /**
     * 提醒人名称
     */
    @Transient
    private String reminder;

    /**
     * 入库信息集合
     */
    @Transient
    private List<DtoConsumableInbound> consumableInboundList;
}