package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.RmsMonitorPlanPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 监测计划点位传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/30
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorPlanPoint")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsMonitorPlanPoint extends RmsMonitorPlanPoint {

    /**
     * 测站名称
     */
    @Transient
    private String stationName;

    /**
     * 水体名称
     */
    @Transient
    private String monitorWaterName;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 点位类型名称
     */
    @Transient
    private String typeCodeName;

    /**
     * 监测类型大类id
     */
    @Transient
    private String bigSampleTypeId;

    /**
     * 受控等级名称
     */
    @Transient
    private String levelCodeName;

    /**
     * 评价标准名称
     */
    @Transient
    private String evaluationCriteriaName;

    /**
     * 评价等级名称
     */
    @Transient
    private String evaluationLevelName;

    /**
     * 关联监测计划数据
     */
    @Transient
    private List<DtoRmsMonitorPlan> relationPlans;

    /**
     * 关联点位数据
     */
    @Transient
    private List<DtoRmsMonitorPlanPoint2Point> relationPoints;
}