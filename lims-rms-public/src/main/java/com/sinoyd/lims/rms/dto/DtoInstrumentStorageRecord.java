package com.sinoyd.lims.rms.dto;


import com.sinoyd.lims.rms.entity.InstrumentStorageRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;


/**
 * 仪器出入库传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentStorageRecord")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentStorageRecord extends InstrumentStorageRecord {

    /**
     * 出入库明细
     */
    @Transient
    private List<DtoInstrumentStorageRecordDetail> details = new ArrayList<>();

    /**
     * 出库数量
     */
    @Transient
    private int outCount;

    /**
     * 入库数量
     */
    @Transient
    private int backCount;

    /**
     * 出库状态
     */
    @Transient
    private boolean outQualifiedFlag = true;

    /**
     * 入库状态
     */
    @Transient
    private boolean backQualified = true;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentNames;
}