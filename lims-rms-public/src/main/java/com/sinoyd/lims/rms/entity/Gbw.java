package com.sinoyd.lims.rms.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinoyd.boot.common.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准物质实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@MappedSuperclass
public class Gbw extends GenericConsumable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否易制毒
     */
    private Boolean isPrecursorChemicals = false;

    /**
     * 是否混标
     */
    private Boolean isMixedStandard = false;

    /**
     * 是否处置
     */
    private Boolean disposeFlag = false;

    /**
     * 处置日期
     */
    @JsonFormat(pattern = DateUtil.YEAR)
    private Date disposeDate;

    /**
     * 处置人
     */
    private String disposerId;

    /**
     * 处置方式
     */
    private String disposeWay;

    /**
     * 稀释液
     */
    private String diluent;

    /**
     * 稀释方法
     */
    private String dilutionMethod;

    /**
     * 保存条件
     */
    private String storageCondition;

    /**
     * 浓度
     */
    private BigDecimal mic;

    /**
     * 浓度偏差（不确定度）
     */
    private BigDecimal micOffset;

    /**
     * 量纲id
     */
    private String dimensionId;
}