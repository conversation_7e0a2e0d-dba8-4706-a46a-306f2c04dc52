package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.EnterpriseMonitorPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 企业点位传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_EnterpriseMonitorPoint")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoEnterpriseMonitorPoint extends EnterpriseMonitorPoint {

    /**
     * 点位经纬度合并
     */
    @Transient
    private String location;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 检测大类id
     */
    @Transient
    private String bigSampleTypeId;

    /**
     * 分析项目名称集合字符串，多个以逗号拼接
     */
    @Transient
    private String analyzeItemNames;

    /**
     * 测站名称
     */
    @Transient
    private String station;

    /**
     * 点位下测试项目集合
     */
    @Transient
    private List<DtoEnterpriseMonitorPointTest> pointTests;
}