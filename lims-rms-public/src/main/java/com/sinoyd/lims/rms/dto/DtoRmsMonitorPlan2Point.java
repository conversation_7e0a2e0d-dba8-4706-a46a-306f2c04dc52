package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.RmsMonitorPlan2Point;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 监测计划和点位关联传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/30
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_MonitorPlan2Point")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsMonitorPlan2Point extends RmsMonitorPlan2Point {

    /**
     * 监测计划名称
     */
    @Transient
    private String planName;

    /**
     * 监测计划年份
     */
    @Transient
    private Integer planYear;

    /**
     * 监测计划月份
     */
    @Transient
    private Integer planMonth;

    /**
     * 点位名称
     */
    @Transient
    private String pointName;

    /**
     * 点位编号
     */
    @Transient
    private String pointSerialNo;

    /**
     * 是否启用
     */
    @Transient
    private Boolean isEnabled;

    /**
     * 分析项目
     */
    @Transient
    private String analyzeItemName;

    /**
     * 检测类型名称
     */
    @Transient
    private String sampleTypeName;

    /**
     * 监测类型大类id
     */
    @Transient
    private String bigSampleTypeId;

    /**
     * 需要添加的关联点位id集合
     */
    @Transient
    private List<String> pointIds;

    /**
     * 新增时的所选测试项目数据
     */
    @Transient
    private List<DtoRmsMonitorPlanPoint2Test> point2Tests;

    /**
     * 监测计划点位id集合（用于新增时，添加测试项目时用到的传参）
     */
    @Transient
    private List<String> plan2PointIds;

    public DtoRmsMonitorPlan2Point(DtoRmsMonitorPlan2Point plan2Point,
                                   String pointName, String pointSerialNo, Boolean isEnabled) {
        BeanUtils.copyProperties(plan2Point, this);
        this.pointName = pointName;
        this.pointSerialNo = pointSerialNo;
        this.isEnabled = isEnabled;
    }

    public DtoRmsMonitorPlan2Point() {

    }
}