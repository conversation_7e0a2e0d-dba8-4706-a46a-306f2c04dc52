package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.RmsSubcontractor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 分包商传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/24
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_Subcontractor")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoRmsSubcontractor extends RmsSubcontractor {

    /**
     * 行政区域名称
     */
    @Transient
    private String areaName;
}