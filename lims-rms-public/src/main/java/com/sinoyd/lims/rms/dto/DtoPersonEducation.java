package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.PersonEducation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 人员教育经历传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_PersonEducation")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoPersonEducation extends PersonEducation {

}