package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.EvaluationCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 评价标准传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/23
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_EvaluationCriteria")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoEvaluationCriteria extends EvaluationCriteria {
    private static final long serialVersionUID = 1L;

    /**
     * 检测类型大类（名称）
     */
    @Transient
    private String sampleTypeName;


    /**
     * 标准类型名称
     */
    @Transient
    private String categoryName;

    /**
     * 状态名称
     */
    @Transient
    private String statusName;
}