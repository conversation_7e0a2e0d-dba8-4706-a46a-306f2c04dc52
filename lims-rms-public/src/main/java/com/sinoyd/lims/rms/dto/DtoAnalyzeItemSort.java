package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.AnalyzeItemSort;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * 分析项目排序传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_AnalyzeItemSort")
@Data
@DynamicInsert
@DynamicUpdate
@Accessors(chain = true)
public class DtoAnalyzeItemSort extends AnalyzeItemSort {

    /**
     * 排序详情
     */
    @Transient
    private List<DtoAnalyzeItemSortDetail> analyzeItemSortDetailList;
}