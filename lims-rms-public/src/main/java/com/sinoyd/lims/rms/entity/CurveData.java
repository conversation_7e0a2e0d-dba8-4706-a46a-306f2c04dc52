package com.sinoyd.lims.rms.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 标准曲线数据表实体;
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/02/19
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CurveData extends LimsBaseEntity{

    private static final long serialVersionUID = 1L;

    public CurveData() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     *主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     *标准曲线id
     */
    private String curveId;

    /**
     *标准溶液加入体积
     */
    private String liquorVolume;

    /**
     *X值
     */
    private String valueX;

    /**
     *Y值
     */
    private String valueY;

    /**
     *220吸光度
     */
    private String od220;

    /**
     *275吸光度
     */
    private String od275;

    /**
     *减空白吸光度
     */
    private String odSubtractBlank;

    /**
     *背景吸光度
     */
    private String odBackground;

    /**
     *含量值
     */
    private String purityValue;

    /**
     *空白吸光度A0
     */
    private String valueAZero;

    /**
     *显色吸光度A1
     */
    private String valueAOne;

    /**
     *干扰吸光度A2
     */
    private String valueATwo;

    /**
     *假删标识
     */
    private Boolean isDeleted = false;

    /**
     *机构id
     */
    private String orgId;

    /**
     *所属实验室id
     */
    private String domainId;

    /**
     *创建人
     */
    @CreatedBy
    private String creator;

    /**
     *创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     *更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     *更新时间
     */
    @LastModifiedDate
    private Date modifyDate;


}