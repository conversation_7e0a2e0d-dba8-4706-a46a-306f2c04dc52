package com.sinoyd.lims.rms.entity;

import com.sinoyd.base.entity.LimsBaseEntity;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

/**
 * 标准物质实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@Accessors(chain = true)
@EntityListeners(AuditingEntityListener.class)
public class GbwMixed extends LimsBaseEntity {

    private static final long serialVersionUID = 1L;

    public GbwMixed() {
        this.orgId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
        this.domainId = StringUtils.isNotNull(PrincipalContextUser.getPrincipal()) ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键
     */
    @Id
    private String id = UUIDHelper.newId();

    /**
     * 标准物质id
     */
    private String gbwId;

    /**
     * 分析项目id
     */
    private String analyzeItemId;

    /**
     * 量纲id
     */
    private String dimensionId;

    /**
     * 浓度
     */
    private String mic;

    /**
     * 不确定度(浓度偏差)
     */
    private String micOffset;

    /**
     * 假删标识
     */
    private Boolean isDeleted;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 所属实验室id
     */
    private String domainId;


    /**
     * 创建人
     */
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
