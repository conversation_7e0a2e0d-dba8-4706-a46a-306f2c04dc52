package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件管理权限
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumFolderAuthority {
    添加文件("addFolder", "LIM_DocumentAuthorityType_AddFolder"),
    更新文件夹("updateFolder", "LIM_DocumentAuthorityType_UpdateFolder"),
    删除文件夹("deleteFolder", "LIM_DocumentAuthorityType_DeleteFolder"),
    删除文件("deleteFile", "LIM_DocumentAuthorityType_DeleteFile"),
    上传权限("upload", "LIM_DocumentAuthorityType_Upload"),
    下载权限("download", "LIM_DocumentAuthorityType_Download"),
    管理权限("root", "LIM_DocAuthority_Show");
    private String value;
    private String code;

    public static String getValueByCode(String code) {
        for (EnumFolderAuthority enumFolderAuthority : EnumFolderAuthority.values()) {
            if (enumFolderAuthority.getCode().equals(code)) {
                return enumFolderAuthority.value;
            }
        }
        return "";
    }
}
