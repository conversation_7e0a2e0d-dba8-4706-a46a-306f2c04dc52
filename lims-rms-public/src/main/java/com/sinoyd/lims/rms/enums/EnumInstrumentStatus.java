package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仪器状态枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumInstrumentStatus {
    全部(-1, "", ""),
    报废(0, "warning", "color: var(--el-color-warning)"),
    正常(1, "success", "color: var(--el-color-success)"),
    停用(2, "info", "color: var(--el-color-info)");

    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * tag， 前端使用
     */
    private final String tag;

    /**
     * 样式，前端使用
     */
    private final String style;

    public static String getNameByStatus(Integer value) {
        for (EnumInstrumentStatus c : EnumInstrumentStatus.values()) {
            if (c.value.equals(value)) {
                return c.name();
            }
        }
        return "";
    }
}
