package com.sinoyd.lims.rms.dto;

import com.sinoyd.base.vo.TreeNodeVO;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.lims.rms.entity.SampleType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;


/**
 * 检测类型传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_SampleType")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoSampleType extends SampleType {

    /**
     * 行业类型名称
     */
    @Transient
    private String industryTypeName;

    /**
     * 将检测类型实体转换成树结构
     * <p>其中，id为检测类型id， label为检测类型名称，type固定为检测类型，供前端进行标识， category为检测类型上的category</p>
     *
     * @return 检测类型树
     */
    public TreeNodeVO loadTreeNodeVO() {
        return new TreeNodeVO()
                .setId(getId())
                .setParentId(getParentId())
                .setLabel(getTypeName())
                .setType("检测类型")
                .setCategory(getCategory())
                .setOrderNum(getOrderNum());
    }
}

