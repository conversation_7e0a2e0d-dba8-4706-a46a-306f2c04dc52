package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.TestPosition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 测试岗位传输对象
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_TestPosition")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoTestPosition extends TestPosition {

    /**
     * 岗位人员关联数据
     */
    @Transient
    private List<DtoTestPositionPerson> positionPeople;

}
