package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 子公式的父级类型，项目中子公式的父级有DtoFormulaConfig(通用公式)
 * 和RecordConfigPersonalizedData(原始记录单个性化数据)两张表
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumPartFormulaParentType {
    通用公式(0),
    原始记录单个性化数据(1);
    private Integer value;
}
