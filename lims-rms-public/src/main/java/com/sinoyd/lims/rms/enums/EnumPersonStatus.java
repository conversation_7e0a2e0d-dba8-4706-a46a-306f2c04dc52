package com.sinoyd.lims.rms.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员的状态（离职，在职，休假）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@Getter
public enum EnumPersonStatus {
    在职(1),
    离职(2),
    休假(3);
    private Integer value;

    private String label = this.name();

    EnumPersonStatus(Integer value) {
        this.value = value;
    }

    public static List<Integer> allValueList = new ArrayList<>();

    static {
        for (EnumPersonStatus c : EnumPersonStatus.values()) {
            allValueList.add(c.value);
        }
    }

    public static EnumPersonStatus getEnumPersonStatus(Integer value) {
        for (EnumPersonStatus c : EnumPersonStatus.values()) {
            if (c.value.equals(value)) {
                return c;
            }
        }
        return null;
    }

    public static EnumPersonStatus getEnumPersonStatus(String label) {
        for (EnumPersonStatus c : EnumPersonStatus.values()) {
            if (c.label.equals(label)) {
                return c;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> names = new ArrayList<>();
        for (EnumPersonStatus c : EnumPersonStatus.values()) {
            names.add(c.name());
        }
        return names;
    }

}
