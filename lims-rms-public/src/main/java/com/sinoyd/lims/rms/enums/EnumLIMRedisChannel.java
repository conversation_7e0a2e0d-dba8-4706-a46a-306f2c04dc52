package com.sinoyd.lims.rms.enums;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * LIM相关的redis key 通道的定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumLIMRedisChannel {

    //测试人员配置修改之后的通道
    LIM_Person2Test_Save,
    //合同修改删除的通道
    LIM_Contract_UpdateDelete,
    //公共的缓存
    LIM_Notice_Cache,
    //快速导航缓存
    LIM_FastNavigation_Cache;
}
