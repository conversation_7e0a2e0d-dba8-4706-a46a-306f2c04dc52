package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentInventory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 仪器出入库传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/6/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentInventory")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentInventory extends InstrumentInventory {

    /**
     * 使用人员id集合
     */
    @Transient
    private List<String> userIds;

    /**
     * 用途集合
     */
    @Transient
    private List<DtoInstrumentOutPurpose> instrumentOutPurposeList;

    /**
     * 绑定设备集合
     */
    @Transient
    private List<DtoInstrumentOutDevice> deviceList;

    /**
     * 出库确认数量
     */
    @Transient
    private Long outConfirmCount;

    /**
     * 已入库数量
     */
    @Transient
    private Long inCount;

    /**
     * 出库设备总数
     */
    @Transient
    private Integer totalDeviceCount;

    /**
     * 使用人员名称，多个用英文逗号拼接
     */
    @Transient
    private String usePersonNames;

    /**
     * 管理人员名称
     */
    @Transient
    private String managerName;

    /**
     * 项目编号
     */
    @Transient
    private String projectCode;

    /**
     * 仪器用途，多个用英文逗号拼接
     * 如果选的是项目就是项目名称，手填的直接获取 {@link DtoInstrumentOutPurpose}中属性otherPurpose值
     */
    @Transient
    private String purposes;

    /**
     * 仪器设备名称;
     */
    @Transient
    private String deviceNameModel;
}