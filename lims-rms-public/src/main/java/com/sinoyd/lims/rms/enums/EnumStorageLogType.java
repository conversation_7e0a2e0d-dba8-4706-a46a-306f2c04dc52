package com.sinoyd.lims.rms.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消耗品库存日志操作类型
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/10
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumStorageLogType {
    全部(-1),
    领用(1),
    处置(2),
    入库(3),
    修改库存(4),
    删除(5),
    入库记录删除(6);

    private int value;

    public static String getNameByValue(int value) {
        String result = "";
        for (EnumStorageLogType operationType : EnumStorageLogType.values()) {
            if (operationType.value == value) {
                result = operationType.name();
                break;
            }
        }
        return result;
    }
}
