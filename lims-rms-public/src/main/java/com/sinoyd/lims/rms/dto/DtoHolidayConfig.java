package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.HolidayConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 节假日配置传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/4/24
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_HolidayConfig")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoHolidayConfig extends HolidayConfig {
}