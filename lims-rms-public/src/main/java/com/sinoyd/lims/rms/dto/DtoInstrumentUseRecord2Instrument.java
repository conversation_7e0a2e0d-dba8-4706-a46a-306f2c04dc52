package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentUseRecord2Instrument;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 仪器使用记录关联仪器表传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/6/21
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentUseRecord2Instrument")
@Data
@DynamicInsert
@Where(clause = "isDeleted = 0")
@Accessors(chain = true)
public class DtoInstrumentUseRecord2Instrument extends InstrumentUseRecord2Instrument {

    /**
     * 图片转化后的base64的字符串
     */
    @Transient
    private String base64Content;

    /**
     * 仪器名称
     */
    @Transient
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Transient
    private String instrumentCode;

    /**
     * 仪器型号
     */
    @Transient
    private String instrumentModel;

    /**
     * 仪器出厂编号
     */
    @Transient
    private String serialNo;

    /**
     * 仪器检定校准结果
     */
    @Transient
    private Boolean inspectResult;

    /**
     * 仪器状态名称
     */
    @Transient
    private String statusName;

    /**
     * 过期状态名称
     */
    @Transient
    private String dateStatusName;

    /**
     * 设置仪器相关数据
     *
     * @param instrument 仪器信息
     */
    public void setInstrument(DtoInstrument instrument) {
        this.base64Content = instrument.getBase64Content();
        this.instrumentName = instrument.getName();
        this.instrumentCode = instrument.getCode();
        this.instrumentModel = instrument.getModel();
        this.serialNo = instrument.getSerialNo();
        this.statusName = instrument.getStatusName();
        this.dateStatusName = instrument.getDateStatusName();
        this.inspectResult = instrument.getInstrumentInspectionPlan().getInspectResult();
    }


    /**
     * 无参构造函数
     */
    public DtoInstrumentUseRecord2Instrument() {
        super();
    }

    /**
     * 构造函数（关联仪器以及使用记录）
     *
     * @param useRecordId  仪器使用记录id
     * @param instrumentId 仪器id
     */
    public DtoInstrumentUseRecord2Instrument(String useRecordId, String instrumentId) {
        this();
        setInstrumentUseRecordId(useRecordId);
        setInstrumentId(instrumentId);
    }
}