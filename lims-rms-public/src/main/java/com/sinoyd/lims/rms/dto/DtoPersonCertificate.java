package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.PersonCertificate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;


/**
 * 证书管理传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/22
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_PersonCertificate")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoPersonCertificate extends PersonCertificate {

    /**
     * 持有人员姓名
     */
    @Transient
    private String personName;

    /**
     * 持有人员用户编号
     */
    @Transient
    private String personNo;

    /**
     * 状态名称
     */
    @Transient
    private String statusName;

    /**
     * 证书检测能力
     */
    @Transient
    private List<DtoPersonCertificateAbility> abilities;

    /**
     * 分析项目名称集合
     */
    @Transient
    private List<String> analyzeItemNames;

}