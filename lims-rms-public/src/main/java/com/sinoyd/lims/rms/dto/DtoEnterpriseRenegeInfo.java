package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.EnterpriseRenegeInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 企业违约信息传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_EnterpriseRenegeInfo")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoEnterpriseRenegeInfo extends EnterpriseRenegeInfo {


    /**
     * 处理人名称
     */
    @Transient
    private String processedPersonName;

    /**
     * 登记人名称
     */
    @Transient
    private String recordPersonName;
}