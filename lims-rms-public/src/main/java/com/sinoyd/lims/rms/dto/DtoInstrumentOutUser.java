package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.InstrumentOutUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 仪器出库使用人传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/6/10
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_InstrumentOutUser")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoInstrumentOutUser extends InstrumentOutUser {

    /**
     * 使用人员名称
     */
    @Transient
    private String userName;

    /**
     * 默认构造器
     */
    public DtoInstrumentOutUser() {
    }

    /**
     * 构造器
     *
     * @param instrumentOutId 仪器出库记录id
     * @param userId          使用人员id
     */
    public DtoInstrumentOutUser(String instrumentOutId, String userId) {
        setInstrumentInventoryId(instrumentOutId);
        setUserId(userId);
    }
}