package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.ProjectTypeConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 项目类型配置传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/6/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_ProjectTypeConfig")
@Data
@DynamicInsert
@ToString
@Accessors(chain = true)
public class DtoProjectTypeConfig extends ProjectTypeConfig {

    private static final long serialVersionUID = 1L;



}