package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.ConsumableInbound;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 消耗品入库信息传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/20
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_ConsumableInbound")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoConsumableInbound extends ConsumableInbound {

    /**
     * 验收结论名称
     */
    @Transient
    private String caResultName;
}