package com.sinoyd.lims.rms.dto;

import com.sinoyd.lims.rms.entity.EvaluationValue;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;

import java.util.ArrayList;
import java.util.List;


/**
 * 评价限值传输实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/12/23
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_RMS_EvaluationValue")
@Data
@DynamicInsert
@Accessors(chain = true)
public class DtoEvaluationValue extends EvaluationValue {

    /**
     * 分析项目名称
     */
    @Transient
    private String analyzeItemName;

    /**
     * 分析项目id集合
     */
    @Transient
    private List<String> analyzeItemIds = new ArrayList<>();

    /**
     * 评价限值id容器
     */
    @Transient
    private List<String> operationIds;

    /**
     * 目标评价等级id列表，用于复制分析项目
     */
    @Transient
    private List<String> targetLevelIds;

    /**
     * 复制的评价等级id
     */
    @Transient
    private String sourceLevelId;

    /**
     * 量纲
     */
    @Transient
    private String dimensionName;
}