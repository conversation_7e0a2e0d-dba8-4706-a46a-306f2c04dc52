package com.sinoyd.lims.rms.service.impl;

import com.sinoyd.base.constants.IEventAction;
import com.sinoyd.base.event.LIMSEvent;
import com.sinoyd.base.service.impl.LimsBaseServiceImpl;
import com.sinoyd.base.vo.GenericDropdownVO;
import com.sinoyd.boot.common.context.SpringContextAware;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.*;
import com.sinoyd.lims.rms.constant.IRMSEventAction;
import com.sinoyd.lims.rms.constant.IEventName;
import com.sinoyd.lims.rms.dto.*;
import com.sinoyd.lims.rms.repository.EnterpriseMonitorPointRepository;
import com.sinoyd.lims.rms.vo.GenericPointVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 企业点位业务接口实现
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/30
 */
@Service
public class EnterpriseMonitorPointServiceImpl
        extends LimsBaseServiceImpl<DtoEnterpriseMonitorPoint, String, EnterpriseMonitorPointRepository>
        implements EnterpriseMonitorPointService {

    private SampleTypeService sampleTypeService;

    private EnterpriseMonitorPointTestService pointTestService;

    private TestService testService;

    private MonitorStationService monitorStationService;

    /**
     * 分页查询
     *
     * @param page     分页参数
     * @param criteria 查询条件
     */
    @Override
    public void findByPage(PageBean<DtoEnterpriseMonitorPoint> page, BaseCriteria criteria) {
        page.setEntityName("DtoEnterpriseMonitorPoint emp, DtoSampleType st");
        page.setSelect("select emp");
        super.findByPage(page, criteria);
    }

    /**
     * 保存数据
     *
     * @param entity 实体
     * @return 保存后数据
     */
    @Override
    @Transactional
    public DtoEnterpriseMonitorPoint save(DtoEnterpriseMonitorPoint entity) {
        //校验数据
        verifyData(entity);
        //处理数据
        handleData(entity);
        return super.save(entity);
    }

    /**
     * 更新数据
     *
     * @param entity 实体
     * @return 更新后数据
     */
    @Override
    @Transactional
    public DtoEnterpriseMonitorPoint update(DtoEnterpriseMonitorPoint entity) {
        //校验数据
        verifyData(entity);
        //处理数据
        handleData(entity);
        return super.update(entity);
    }

    /**
     * 根据企业id集合删除企业点位数据
     *
     * @param enterpriseIds 企业id集合
     */
    @Override
    @Transactional
    public void deleteByEnterpriseIdIn(Collection<String> enterpriseIds) {
        List<DtoEnterpriseMonitorPoint> pointList = repository.findByEnterpriseIdInAndIsDeletedFalse(enterpriseIds);
        logicDeleteById(pointList.stream().map(DtoEnterpriseMonitorPoint::getId).collect(Collectors.toSet()));
    }

    /**
     * 删除企业点位数据
     *
     * @param strings 企业点位id集合
     * @return 删除成功的条数
     */
    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        //点位删除事件发布
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(strings,
                IEventName.ENTERPRISE_POINT, IEventAction.DELETE));
        return super.logicDeleteById(strings);
    }


    /**
     * 查询点位下的测试项目交集
     *
     * @param sonPointIds 子点位id列表
     * @return 测试项目列表
     */
    @Override
    public List<GenericPointVO.PointTestListVO> queryTestsOfPoints(Collection<String> sonPointIds) {
        //查询点位下的测试项目交集
        List<GenericPointVO.PointTestListVO> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(sonPointIds)) {
            List<DtoEnterpriseMonitorPointTest> pointTests = pointTestService.findByPointIdIn(sonPointIds);
            List<String> testIds = pointTests.stream().map(DtoEnterpriseMonitorPointTest::getTestId)
                    .distinct().collect(Collectors.toList());
            List<DtoTest> tests = testService.findAll(testIds);
            for (DtoTest test : tests) {
                GenericPointVO.PointTestListVO item = new GenericPointVO.PointTestListVO();
                BeanUtils.copyProperties(test, item);
                Optional<DtoEnterpriseMonitorPointTest> pointTestOp = pointTests.stream()
                        .filter(g -> g.getTestId().equals(item.getId())).findFirst();
                if (pointTestOp.isPresent()) {
                    DtoEnterpriseMonitorPointTest pointTest = pointTestOp.get();
                    item.setSubcontractType(pointTest.getSubcontractType());
                    Boolean isEvaluate = !(StringUtils.isEmpty(pointTest.getEvaluationCriteriaId()) || UUIDHelper.guidEmpty().equals(pointTest.getEvaluationCriteriaId()));
                    item.setIsEvaluate(isEvaluate);
                }
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 批量设置批次
     *
     * @param countOperationVO 批次批量操作VO
     */
    @Override
    @Transactional
    public void batchSampleCount(GenericPointVO.BatchCountOperationVO countOperationVO) {
        List<String> pointIds = countOperationVO.getPointIds();
        List<DtoEnterpriseMonitorPoint> entPointList = findAll(pointIds);
        //判断是否需要更新点位周期
        if (StringUtils.isNotNull(countOperationVO.getCycleNum())) {
            entPointList.forEach(p -> p.setPeriod(countOperationVO.getCycleNum()));
            update(entPointList);
        }
        //发布点位批量更新样次事件
        SpringContextAware.getApplicationContext().publishEvent(new LIMSEvent<>(countOperationVO, IEventName.ENTERPRISE_POINT, IRMSEventAction.BATCH_UPDATE_COUNT));
    }

    /**
     * 检测类型下拉框数据
     *
     * @param enterpriseId 企业id
     * @return 通用下拉框数据
     */
    @Override
    public List<GenericDropdownVO> sampleTypeDropdown(String enterpriseId) {
        List<GenericDropdownVO> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(enterpriseId)) {
            List<DtoEnterpriseMonitorPoint> enterprisePoints = repository.findByEnterpriseIdInAndIsDeletedFalse(Stream.of(enterpriseId).collect(Collectors.toList()));
            Set<String> sampleTypeIds =  enterprisePoints.stream().map(DtoEnterpriseMonitorPoint::getSampleTypeId).collect(Collectors.toSet());
            if (StringUtils.isNotEmpty(sampleTypeIds)) {
                List<DtoSampleType> sampleTypeMap = sampleTypeService.findAll(sampleTypeIds);
                for (DtoSampleType sampleType : sampleTypeMap) {
                    result.add(new GenericDropdownVO()
                            .setLabel(sampleType.getTypeName())
                            .setValue(sampleType.getId()));
                }
            }
        }
        return result;
    }

    /**
     * 加载冗余字段
     *
     * @param collection 企业点位数据
     */
    @Override
    public void loadTransientFields(Collection<DtoEnterpriseMonitorPoint> collection) {
        //点位id集合
        Set<String> pointIds = collection.stream().map(DtoEnterpriseMonitorPoint::getId).collect(Collectors.toSet());
        Set<String> sampleTypeIds = new HashSet<>();
        collection.forEach(t -> {
            if (StringUtils.isNotEmpty(t.getSampleTypeId())) {
                sampleTypeIds.add(t.getSampleTypeId());
            }
        });
        Map<String, DtoSampleType> sampleTypes = new ConcurrentHashMap<>();
        if (StringUtils.isNotEmpty(sampleTypeIds)) {
            sampleTypes = sampleTypeService.findAllMap(sampleTypeIds);
        }
        //获取所有点位下的点位测试项目
        List<DtoEnterpriseMonitorPointTest> pointTests = pointTestService.findByPointIdIn(pointIds);
        //测站数据
        Set<String> stationIds = collection.stream().map(DtoEnterpriseMonitorPoint::getStationId).collect(Collectors.toSet());
        Map<String, DtoMonitorStation> stationMap = monitorStationService.findAllMap(stationIds, false);
        for (DtoEnterpriseMonitorPoint point : collection) {
            DtoSampleType sampleType = sampleTypes.get(point.getSampleTypeId());
            point.setSampleTypeName(sampleType == null ? "" : sampleType.getTypeName());
            point.setBigSampleTypeId(sampleType == null ? "" : sampleType.getParentId());
            //处理点位的经纬度合并
            if (StringUtils.isNotEmpty(point.getLongitude()) && StringUtils.isNotEmpty(point.getLatitude())) {
                String location = point.getLongitude() + "," + point.getLatitude();
                point.setLocation(location);
            }
            //设置点位下的测试项目集合
            List<DtoEnterpriseMonitorPointTest> singlePointTest = pointTests.stream().filter(p->point.getId().equals(p.getEnterprisePointId())).collect(Collectors.toList());
            singlePointTest = pointTestService.processParentTest(singlePointTest);
            point.setPointTests(singlePointTest);
            //设置点位的分析项目名称冗余字段
            List<String> analyzeItemNames = singlePointTest.stream().map(DtoEnterpriseMonitorPointTest::getAnalyzeItemName).collect(Collectors.toList());
            point.setAnalyzeItemNames(String.join(",", analyzeItemNames));
            point.setStation(stationMap.get(point.getStationId()) == null ? "" : stationMap.get(point.getStationId()).getName());
        }
    }

    /**
     * 数据校验
     *
     * @param monitorPoint 企业点位数据
     */
    private void verifyData(DtoEnterpriseMonitorPoint monitorPoint) {
        Integer count = repository.countByPointNameAndEnterpriseIdAndIdNotAndIsDeletedFalse(monitorPoint.getPointName(), monitorPoint.getEnterpriseId(), monitorPoint.getId());
        if (count > 0) {
            throw new BaseException(String.format("点位[%s]已存在!", monitorPoint.getPointName()));
        }
    }

    /**
     * 数据处理
     *
     * @param monitorPoint 企业点位数据
     */
    private void handleData(DtoEnterpriseMonitorPoint monitorPoint) {
        String location = monitorPoint.getLocation();
        if (StringUtils.isNotEmpty(location)) {
            if (location.contains(",")) {
                List<String> locationList = Arrays.stream(location.split(",")).collect(Collectors.toList());
                monitorPoint.setLongitude(locationList.get(0));
                monitorPoint.setLatitude(locationList.get(1));
            } else {
                throw new BaseException("经纬度格式错误,正确格式应为[经度,维度]!)");
            }
        }
    }

    @Autowired
    public void setSampleTypeService(SampleTypeService sampleTypeService) {
        this.sampleTypeService = sampleTypeService;
    }

    @Autowired
    public void setPointTestService(EnterpriseMonitorPointTestService pointTestService) {
        this.pointTestService = pointTestService;
    }

    @Autowired
    public void setTestService(TestService testService) {
        this.testService = testService;
    }

    @Autowired
    public void setMonitorStationService(MonitorStationService monitorStationService) {
        this.monitorStationService = monitorStationService;
    }
}
