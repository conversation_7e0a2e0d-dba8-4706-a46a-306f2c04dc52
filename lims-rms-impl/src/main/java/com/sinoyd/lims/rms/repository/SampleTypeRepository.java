package com.sinoyd.lims.rms.repository;

import com.sinoyd.base.repository.LimsRepository;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import com.sinoyd.lims.rms.dto.DtoSampleType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 检测类型repository
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/10/12
 */
public interface SampleTypeRepository extends IBaseJpaRepository<DtoSampleType, String>,
        LimsRepository<DtoSampleType, String> {

    /**
     * 根据父id查询子记录id
     *
     * @param parentIds 父id集合
     * @return 子记录id集合
     */
    @Query("select p.id from DtoSampleType p where p.parentId in :parentIdList")
    List<String> findChildIds(@Param("parentIdList") Collection<String> parentIds);

    /**
     * 根据行业类型id查询检测类型id
     *
     * @param industryTypeIds 行业类型id集合
     * @return 检测类型id
     */
    @Query("select p.id from DtoSampleType p where p.industryTypeId in :industryTypeIds and p.isDeleted = false")
    List<String> findIdByIndustryTypeIds(@Param("industryTypeIds") Collection<String> industryTypeIds);

    /**
     * 根据行业类型id和类型查询
     *
     * @param industryTypeId 行业类型id
     * @param category       检测类型种类
     * @return 检测类型
     */
    @Query("select p from DtoSampleType p where p.industryTypeId = :industryTypeId " +
            "and p.isDeleted = false and p.category = :category order by p.orderNum desc, p.typeName")
    List<DtoSampleType> findByIndustryTypeIdAndCategory(@Param("industryTypeId") String industryTypeId,
                                                        @Param("category") Integer category);

    /**
     * 根据类型查询
     *
     * @param category 检测类型种类
     * @return 检测类型
     */
    @Query("select p from DtoSampleType p where p.isDeleted = false and p.category = :category order by p.orderNum desc, p.typeName")
    List<DtoSampleType> findByCategory(@Param("category") Integer category);

    /**
     * 根据类型名称、父级、id统计数据
     *
     * @param typeName 类型名称
     * @param id       id
     * @return 统计数量
     */
    @Query("select count(p.id) from DtoSampleType p where p.typeName = :typeName " +
            "and p.isDeleted = false and p.id <> :id ")
    Integer countForSaveValidation(@Param("typeName") String typeName, @Param("id") String id);

    /**
     * 查询小类
     *
     * @param parentId 父id
     * @param category 分类，1、检测类型大类 2、检测类型小类
     * @return
     */
    @Query("select p from DtoSampleType p where p.parentId = :parentId and p.isDeleted = false " +
            "and p.category = :category order by p.orderNum desc, p.typeName")
    List<DtoSampleType> findSmallSampleType(@Param("parentId") String parentId, @Param("category") Integer category);

    /**
     * 查询所有未删除的检测类型
     *
     * @return 检测类型集合
     */
    List<DtoSampleType> findByIsDeletedFalse();

    /**
     * 根据分瓶规则查询
     *
     * @param sampleGroupRuleIds 分瓶规则id集合
     * @return 检测类型
     */
    @Query("select p from DtoSampleType p where p.isDeleted = false " +
            "and (p.defaultLabelGroupId in :sampleGroupRuleIds or p.fieldTaskGroupId in :sampleGroupRuleIds)")
    List<DtoSampleType> findBySampleGroupRule(@Param("sampleGroupRuleIds") Collection<String> sampleGroupRuleIds);

    /**
     * 根据行业类型id查询
     *
     * @param industryTypeId 行业类型id
     * @return 检测类型
     */
    List<DtoSampleType> findByIsDeletedFalseAndIndustryTypeId(String industryTypeId);
}
