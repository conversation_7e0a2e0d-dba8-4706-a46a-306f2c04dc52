package com.sinoyd.lims.rms.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.base.enums.*;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 证书管理查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2022/11/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PersonCertificateCriteria extends LIMSBaseCriteria {

    /**
     * 人员id
     */
    private String personId;

    /**
     * 比较时间，service中进行填充用于状态过滤
     */
    private Date compareDate;

    /**
     * 有效期查询开始时间
     */
    private String startTime;

    /**
     * 有效期查询结束时间
     */
    private String endTime;

    /**
     * 关键字查询，可以对证书名称、证书编号进行模糊查询
     */
    private String key;

    /**
     * 分析方法关键字查询
     */
    private String methodKey;

    /**
     * 分析项目关键字查询
     */
    private String itemKey;

    /**
     * 状态查询
     */
    private Integer status;

    @Override
    public String getCondition() {
        values.clear();
        StringBuilder condition = new StringBuilder();
        //有效期查询开始时间查询
        if (StringUtils.isNotEmpty(this.startTime)) {
            Date from = DateUtil.stringToDate(this.startTime, DateUtil.YEAR);
            condition.append(" and c.issueDate >= :startTime");
            values.put("startTime", from);
        }
        //有效期查询结束时间查询
        if (StringUtils.isNotEmpty(this.endTime)) {
            condition.append(" and c.issueDate < :endTime");
            values.put("endTime", processEndDate(this.endTime, DateUtil.YEAR));
        }
        if (StringUtils.isNotEmpty(this.personId)) {
            condition.append(" and personId = :personId");
            values.put("personId", this.personId);
        }
        if (StringUtils.isNotEmpty(this.key)) {
            condition.append(" and (c.name like :key or c.certificationNo like :key)");
            values.put("key", appendPercent(this.key));
        }
        if (StringUtils.isNotEmpty(this.methodKey)) {
            condition.append(" and exists( select 1 from DtoPersonCertificateAbility abt where abt.personCertId = c.id " +
                    "and exists (" +
                    "select 1 from DtoTest t where t.id = abt.testId " +
                    "and (t.analyzeMethodName like :methodKey or t.methodStandardNo like :methodKey)" +
                    ")" +
                    ")");
            values.put("methodKey", appendPercent(this.methodKey));
        }
        if (StringUtils.isNotEmpty(this.itemKey)) {
            condition.append(" and exists( select 1 from DtoPersonCertificateAbility abt where abt.personCertId = c.id " +
                    "and exists (" +
                    "select 1 from DtoTest t where t.id = abt.testId " +
                    "and (t.chemicalSymbol like :itemKey or t.analyzeItemName like :itemKey or t.pinYin like :itemKey " +
                    "or t.fullPinYin like :itemKey" +
                    ")" +
                    "))");
            values.put("itemKey", appendPercent(this.itemKey));
        }

        /*
          根据检测能力有效期进行过滤，具体规则如下：
          1. 所有检测能力全部正常则正常
          2. 所有检测能力中有一个即将过期即为即将过期
          3. 所有检测能力中有一个为过期，即为过期
         */
        if (this.status != null) {
            if (EnumDateStatus.正常.getValue().equals(this.status)) {
                //所有证书下的检测能力中没有有效期在即将过期期限之前的，此处的compareDate为即将过期期限
                condition.append(" and not exists (select 1 from DtoPersonCertificateAbility a ,DtoTest t " +
                        "where t.id = a.testId and a.personCertId = c.id and a.certEffectiveTime < :compareDate )");
            } else {
                //即将过期和过期判定中，只要检测能力中有一个符合条件即可，compareDate即将过期时为即将过期期限，过期是为当前时间
                condition.append(" and exists (select 1 from DtoPersonCertificateAbility a, DtoTest t where t.id = a.testId and a.personCertId = c.id " +
                        "and a.certEffectiveTime < :compareDate ");
                if (EnumDateStatus.即将过期.getValue().equals(this.status)) {
                    condition.append(" and a.certEffectiveTime >= :now");
                    values.put("now", DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
                }
                condition.append(") ");
            }
            values.put("compareDate", this.compareDate);
        }
        return condition.toString();
    }
}