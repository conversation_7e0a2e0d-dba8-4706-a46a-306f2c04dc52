package com.sinoyd.lims.rms.strategy.resource;

import com.sinoyd.base.enums.EnumDateStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.lims.rms.criteria.ConsumableCriteria;
import com.sinoyd.lims.rms.criteria.GbwCriteria;
import com.sinoyd.lims.rms.dto.DtoConsumableInbound;
import com.sinoyd.lims.rms.dto.DtoGbw;
import com.sinoyd.lims.rms.dto.DtoRmsConsumable;
import com.sinoyd.lims.rms.vo.ConsumableOverdueDetailVO;
import com.sinoyd.lims.rms.vo.ResourceRemindVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 耗材过期提醒策略
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/02/06
 */
@Component
public class ConsumableOverdueRemind extends AbsResourceRemind<ConsumableOverdueDetailVO> {

    @Override
    public ResourceRemindVO statistic() {
        List<DtoRmsConsumable> overdueConsumable = queryOverdueConsumable();
        List<DtoGbw> overdueGbw = queryOverdueGbw();
        Integer overdueCount = overdueConsumable.size() + overdueGbw.size();
        return new ResourceRemindVO("耗材过期", overdueCount, "件");
    }

    @Override
    public List<ConsumableOverdueDetailVO> details() {
        //查询低库存消耗品列表以及库存数据
        List<DtoRmsConsumable> storageConsumable = queryOverdueConsumable();
        //查询低库存标准物质以及库存数据
        List<DtoGbw> storageGbw = queryOverdueGbw();
        List<ConsumableOverdueDetailVO> details = new ArrayList<>();
        //处理消耗品
        for (DtoRmsConsumable consumable : storageConsumable) {
            ConsumableOverdueDetailVO detail = new ConsumableOverdueDetailVO(consumable);
            //获取最早的有效期
            if (StringUtils.isNotEmpty(consumable.getConsumableInboundList())) {
                Date expiryDate = consumable.getConsumableInboundList().stream()
                        .map(DtoConsumableInbound::getExpiryDate)
                        .filter(StringUtils::isNotNull)
                        .min(Date::compareTo)
                        .orElse(null);
                if (expiryDate != null) {
                    detail.setExpiryDate(expiryDate);
                    detail.setOverdueDays(calculateDaysToNow(expiryDate).intValue());
                }
            }
            details.add(detail);
        }
        //处理标准物质
        for (DtoGbw gbw : storageGbw) {
            ConsumableOverdueDetailVO detail = new ConsumableOverdueDetailVO(gbw);
            //获取最早的有效期
            if (gbw.getGbwInbound() != null) {
                Date expiryDate = gbw.getGbwInbound().getExpiryDate();
                if (expiryDate != null) {
                    detail.setExpiryDate(expiryDate);
                    detail.setOverdueDays(calculateDaysToNow(expiryDate).intValue());
                }
            }
            details.add(detail);
        }
        //按照过期天数倒序排序
        if (StringUtils.isNotEmpty(details)){
            details.sort(Comparator.comparing(ConsumableOverdueDetailVO::getOverdueDays, Comparator.reverseOrder()));
        }
        return details;
    }

    /**
     * 查询过期耗材列表
     *
     * @return 过期耗材列表
     */
    public List<DtoRmsConsumable> queryOverdueConsumable() {
        PageBean<DtoRmsConsumable> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        ConsumableCriteria criteria = new ConsumableCriteria();
        criteria.setExpiryStatus(EnumDateStatus.已过期.getValue());
        consumableService.findByPage(pb, criteria);
        return pb.getData();
    }

    /**
     * 查询过期标准物质列表
     *
     * @return 过期标准物质列表
     */
    public List<DtoGbw> queryOverdueGbw() {
        PageBean<DtoGbw> pb = new PageBean<>();
        pb.setPageNo(1);
        pb.setRowsPerPage(Integer.MAX_VALUE);
        GbwCriteria criteria = new GbwCriteria();
        criteria.setDateStatus(EnumDateStatus.已过期.getValue());
        gbwService.findByPage(pb, criteria);
        return pb.getData();
    }
}
