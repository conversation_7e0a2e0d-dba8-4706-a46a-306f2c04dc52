package com.sinoyd.lims.rms.criteria;

import com.sinoyd.base.criteria.LIMSBaseCriteria;
import com.sinoyd.base.enums.EnumDateStatus;
import com.sinoyd.base.enums.EnumStorageStatus;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 标准物质查询条件
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/10/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GbwCriteria extends LIMSBaseCriteria {

    /**
     * 查询关键字(标样名称，标样编号，规格)
     */
    private String key;

    /**
     * 标准物质类型
     */
    private String category;

    /**
     * 提醒人
     */
    private String warningReceiverId;

    /**
     * 是否处置
     */
    private Boolean disposeFlag;

    /**
     * 过期状态，枚举管理，参考 {@link com.sinoyd.base.enums.EnumDateStatus}
     */
    private Integer dateStatus;

    /**
     * 库存状态，枚举管理，参考 {@link com.sinoyd.base.enums.EnumStorageStatus}
     */
    private Integer storageStatus;

    /**
     * 标准物质过期提醒天数(此字段无需前端传递，后端在分页查询时内置进去)
     */
    private int gbwExpireDays;

    @Override
    public String getCondition() {
        StringBuilder condition = new StringBuilder(" and g.isDeleted = 0");
        if (StringUtils.isNotEmpty(this.key)) {
            condition.append(" and (g.name like :key or g.specification like :key or g.serialNo like :key)");
            values.put("key", appendPercent(this.key));
        }
        if (StringUtils.isNotEmpty(this.category)) {
            condition.append(" and g.typeCode = :category");
            values.put("category", this.category);
        }
        if (this.disposeFlag != null) {
            condition.append(" and g.disposeFlag = :disposeFlag");
            values.put("disposeFlag", this.disposeFlag);
        }
        if (StringUtils.isNotEmpty(this.warningReceiverId) && !UUIDHelper.guidEmpty().equals(this.warningReceiverId)) {
            condition.append(" and g.reminderId = :warningReceiverId");
            values.put("warningReceiverId", this.warningReceiverId);
        }

        if (this.dateStatus != null && !this.dateStatus.equals(EnumDateStatus.全部.getValue())) {
            Date compareDate = DateUtil.stringToDate(DateUtil.nowTime(DateUtil.YEAR), DateUtil.YEAR);
            if (EnumDateStatus.已过期.getValue().equals(this.dateStatus)) {
                condition.append(" and exists(select 1 from DtoGbwInbound gi where g.id = gi.gbwId " +
                        "and gi.expiryDate < :compareDate) ");
            } else if (EnumDateStatus.即将过期.getValue().equals(this.dateStatus)) {
                compareDate = DateUtil.dateAdd(new Date(), this.gbwExpireDays, false);
                condition.append(" and exists(select 1 from DtoGbwInbound gi where g.id = gi.gbwId " +
                        "and gi.expiryDate < :compareDate and gi.expiryDate >= :now) ");
                values.put("now", DateUtil.stringToDate(DateUtil.dateToString(new Date(), DateUtil.YEAR), DateUtil.YEAR));
            } else {
                //正常状态(包含即将过期)
                condition.append(" and exists(select 1 from DtoGbwInbound gi where g.id = gi.gbwId " +
                        "and gi.expiryDate >= :compareDate) ");
            }
            values.put("compareDate", compareDate);
        }

        if (this.storageStatus != null && !this.storageStatus.equals(EnumStorageStatus.全部.getValue())) {
            if (EnumStorageStatus.无库存.getValue().equals(this.storageStatus)) {
                condition.append(" and g.inventoryAmount <= 0 ");
            } else if (EnumStorageStatus.低库存.getValue().equals(this.storageStatus)) {
                condition.append(" and g.inventoryAmount <= g.warningAmount and g.inventoryAmount > 0 ");
            } else if (EnumStorageStatus.有库存.getValue().equals(this.storageStatus)) {
                condition.append(" and g.inventoryAmount > 0 ");
            }
        }
        return condition.toString();
    }
}
