package com.sinoyd.lims.rms.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.service.impl.LimsBaseExcelServiceImpl;
import com.sinoyd.base.util.BeanUtil;
import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.excel.builder.ImportParamsBuilder;
import com.sinoyd.excel.utils.ExcelUtil;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.lims.rms.RmsSubcontractorService;
import com.sinoyd.lims.rms.constant.IImportConstants;
import com.sinoyd.lims.rms.criteria.RmsSubcontractorCriteria;
import com.sinoyd.lims.rms.dto.DtoRmsSubcontractor;
import com.sinoyd.lims.rms.handler.verify.ImportSubcontractorVerifyHandler;
import com.sinoyd.lims.rms.utils.AreaDropDownListUtil;
import com.sinoyd.lims.rms.utils.EnterPriseImportUtil;
import com.sinoyd.lims.rms.vo.ExcelSubcontractorVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分包商数据导入实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/17
 */
@Service
public class ExcelSubContractorServiceImpl
        extends LimsBaseExcelServiceImpl<DtoRmsSubcontractor, ExcelSubcontractorVO, String, RmsSubcontractorService> {

    private AreaService areaService;


    /**
     * 导入数据
     *
     * @param response 响应体
     * @param file     导入文件
     */
    @Override
    public void importData(HttpServletResponse response, MultipartFile file) {
        ImportSubcontractorVerifyHandler verifyHandler = new ImportSubcontractorVerifyHandler();
        ImportParamsBuilder importParamsBuilder = ImportParamsBuilder.create()
                .pojoClass(ExcelSubcontractorVO.class)
                .verifyHandler(verifyHandler);
        ExcelImportResult<ExcelSubcontractorVO> excelImportResult = pojoImportService.importData(importParamsBuilder.getImportParams(), file, response);
        if (!excelImportResult.isVerifyFail()) {
            List<DtoRmsSubcontractor> importList = turnToEntity(excelImportResult.getList());
            //循环处理关联Id以及初始值
            importList.forEach(EnterPriseImportUtil::handleImportData);
            //处理重复数据
            EnterPriseImportUtil.handleRepeatData(importList, findExportData(new RmsSubcontractorCriteria()));
            this.saveImportData(importList);
        }

    }

    /**
     * 下载导入模板
     *
     * @param response 响应体
     */
    @Override
    public void downloadImportTemplate(HttpServletResponse response) {
        Workbook workbook = pojoImportService.generateTemplateWorkbook(ExcelSubcontractorVO.class, null, null, response);
        //创建区域级联下拉框
        AreaDropDownListUtil.createAreaDropDownList(workbook, areaService.findAreasByCenterConfig(), 2, 3 ,4);
        GlobalExport exportName = ExcelSubcontractorVO.class.getAnnotation(GlobalExport.class);
        if (exportName == null) {
            throw new RuntimeException("尚未配置模板名称");
        }
        String fileName = exportName.name();
        ExcelUtil.downLoadExcel(response, fileName, workbook);
    }

    /**
     * 保存数据
     *
     * @param importList 导入数据集合
     */
    @Transactional
    public void saveImportData(List<DtoRmsSubcontractor> importList) {
        service.save(importList);
    }

    /**
     * 导出数据
     *
     * @param response 响应流
     * @param criteria 查询条件
     */
    @Override
    public void exportData(HttpServletResponse response, BaseCriteria criteria) {
        List<DtoRmsSubcontractor> dataList = findExportData(criteria);
        List<ExcelSubcontractorVO> voList = new ArrayList<>();
        for (DtoRmsSubcontractor enterprise : dataList) {
            ExcelSubcontractorVO enterpriseExcelVo = BeanUtil.convertT2V(ExcelSubcontractorVO.class, enterprise);
            voList.add(enterpriseExcelVo);
        }
        handleExportArea(areaService.findAreasByCenterConfig(), voList);
        super.exportData(response, voList);
    }

    /**
     * 处理导入数据的省市县
     *
     * @param areaList 区域集合
     * @param voList   导出数据集合
     */
    private void handleExportArea(List<DtoArea> areaList, List<ExcelSubcontractorVO> voList) {
        //省级区域Map
        Map<String, DtoArea> areaMap = areaList.stream().collect(Collectors.toMap(DtoArea::getId, area -> area));
        //处理省市县区域名称
        for (ExcelSubcontractorVO exportVo : voList) {
            //清空区域名称
            exportVo.setAreaName(IImportConstants.EMPTY_STRING);
            //获取区域id
            String areaId = exportVo.getAreaId();
            //省级区域名称
            Map<String, String> areaNameMap = new HashMap<>();
            if (areaMap.containsKey(areaId)){
                DtoArea area = areaMap.get(areaId);
                areaNameMap = EnterPriseImportUtil.findExportAreaNames(areaList, area);
            }
            exportVo.setProvinceAreaName(areaNameMap.get(IImportConstants.AreaMapKey.PROVINCE_NAME));
            exportVo.setCityAreaName(areaNameMap.get(IImportConstants.AreaMapKey.CITY_NAME));
            exportVo.setAreaName(areaNameMap.get(IImportConstants.AreaMapKey.AREA_NAME));
        }
    }

    /**
     * 转换导入数据（分包商数据转换）
     *
     * @param importList 导入数据集合
     * @return 实体数据集合
     */
    protected List<DtoRmsSubcontractor> turnToEntity(List<ExcelSubcontractorVO> importList) {
        List<DtoRmsSubcontractor> subcontractorList = new ArrayList<>();
        List<DtoArea> areaList = areaService.findAreasByCenterConfig();
        for (ExcelSubcontractorVO excelVo : importList) {
            //企业数据
            DtoRmsSubcontractor subcontractor = BeanUtil.convertV2T(DtoRmsSubcontractor.class, excelVo);
            //获取区域Id
            String areaId = EnterPriseImportUtil.handleAreaId(
                    areaList, excelVo.getProvinceAreaName(),
                    excelVo.getCityAreaName(), excelVo.getAreaName());
            subcontractor.setAreaId(areaId);
            subcontractorList.add(subcontractor);
        }
        return subcontractorList;
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }
}
