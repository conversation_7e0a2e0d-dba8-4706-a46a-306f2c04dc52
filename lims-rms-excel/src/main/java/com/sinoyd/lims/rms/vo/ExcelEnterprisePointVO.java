package com.sinoyd.lims.rms.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.constants.IValidationMessage;
import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.excel.annotations.DictLoader;
import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.lims.rms.constant.IImportConstants;
import com.sinoyd.lims.rms.loader.dict.EnterpriseDictLoader;
import com.sinoyd.lims.rms.loader.dict.SampleTypeDictLoader;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 企业点位导入/导出操作传输类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@GlobalExport(name = "企业点位一览表")
public class ExcelEnterprisePointVO extends LimsExcelBaseVO {

    /**
     * 点位名称
     */
    @Excel(name = "点位名称", isMandatory = true, width = 20, orderNum = "100")
    @NotNull(message = IValidationMessage.NOT_NULL)
    private String pointName;

    /**
     * 点位编号
     */
    @Excel(name = "点位编号", width = 20, orderNum = "200")
    private String pointSerialNo;

    /**
     * 检测类型
     */
    @Excel(name = "检测类型", dict = IImportConstants.LimDictType.SAMPLE_TYPE, width = 20, orderNum = "300", addressList = true)
    @DictLoader(loaderClass = SampleTypeDictLoader.class)
    private String sampleTypeId;

    /**
     * 周期
     */
    @Excel(name = "周期", isMandatory = true, width = 20, orderNum = "400")
    @NotNull(message = IValidationMessage.NOT_NULL)
    private Integer period;

    /**
     * 所属测站
     */
    @Excel(name = "所属测站", width = 20, orderNum = "500")
    private String station;

    /**
     * 测站id
     */
    private String stationId;

    /**
     * 经度
     */
    @Excel(name = "经度", width = 20, orderNum = "600")
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度", width = 20, orderNum = "610")
    private String latitude;

    /**
     * 控制等级，常量lims_rms_controlLevel
     */
    @Excel(name = "控制等级", dict = "lims_rms_controlLevel", width = 20, orderNum = "700", addressList = true)
    private String levelCode;

    /**
     * 是否启用
     */
    @Excel(name = "状态启用", replace = {"启用_true", "禁用_false"}, width = 12, orderNum = "800", addressList = true)
    private Boolean isEnabled = true;

    /**
     * 获取唯一性字段名称，如果是组合字段唯一性，多个字段之间用##拼接，比如 name##age的形式
     *
     * @return 唯一性字段集合
     */
    @Override
    public Set<String> getUniqueFieldNames() {
        return Stream.of("pointName").collect(Collectors.toSet());
    }

    /**
     * 企业Id
     */
    @Excel(name = "企业名称", dict = IImportConstants.LimDictType.ENTERPRISE, width = 25, orderNum = "210", addressList = true)
    @DictLoader(loaderClass = EnterpriseDictLoader.class)
    private String enterpriseId;

}
