package com.sinoyd.lims.rms.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.excel.annotations.DictLoader;
import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.lims.rms.constant.IImportConstants;
import com.sinoyd.lims.rms.loader.dict.AnalyzeItemLoader;
import com.sinoyd.lims.rms.loader.dict.AnalyzeMethodLoader;
import com.sinoyd.lims.rms.loader.dict.DimensionLoader;
import com.sinoyd.lims.rms.loader.dict.SampleBigTypeDictLoader;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 测试项目导入/导出操作传输类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/09/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@GlobalExport(name = "测试项目一览表")
public class ExcelTestVO extends LimsExcelBaseVO {

    /**
     * 分析方法id
     */
    @Excel(name = "分析方法", dict = IImportConstants.LimDictType.ANALYZE_METHOD, isMandatory = true, orderNum = "200", width = 35, addressList = true)
    @DictLoader(loaderClass = AnalyzeMethodLoader.class)
    private String analyzeMethodId;

    /**
     * 分析方法名称
     */
    private String analyzeMethodName;

    /**
     * 标准编号
     */
    private String methodStandardNo;

    /**
     * 分析项目id
     */
    @Excel(name = "分析因子", dict = IImportConstants.LimDictType.ANALYZE_ITEM, isMandatory = true, orderNum = "500", width = 25, addressList = true)
    @DictLoader(loaderClass = AnalyzeItemLoader.class)
    private String analyzeItemId;

    /**
     * 分析项目名称
     */
    private String analyzeItemName;

    /**
     * 分析项目化学符号
     */
    private String chemicalSymbol;

    /**
     * 分析项目全拼
     */
    private String fullPinYin;

    /**
     * 分析项目拼音缩写
     */
    private String pinYin;

    /**
     * 测试编码
     */
    private String testCode;

    /**
     * 样品类型（Guid）
     */
    @Excel(name = "检测类型", dict = IImportConstants.LimDictType.SAMPLE_BIG_TYPE, isMandatory = true, orderNum = "600", width = 20, addressList = true)
    @DictLoader(loaderClass = SampleBigTypeDictLoader.class)
    private String sampleTypeId;

    /**
     * 计量单位（Guid）
     */
    @Excel(name = "量纲", dict = IImportConstants.LimDictType.DIMENSION, orderNum = "700", width = 15, addressList = true)
    @DictLoader(loaderClass = DimensionLoader.class)
    private String dimensionId;

    /**
     * 检出限
     */
    @Excel(name = "检出限", orderNum = "800")
    private String detectionValue;

    /**
     * 有效位数
     */
    @Excel(name = "有效位数", isMandatory = true, orderNum = "900")
    private Integer significantDigit;

    /**
     * 小数位数
     */
    @Excel(name = "小数位数", isMandatory = true, orderNum = "1000")
    private Integer decimalDigit;

    /**
     * 样品有效期（h）
     */
    @Excel(name = "有效期（小时）", orderNum = "1100", width = 22)
    private BigDecimal validTime = new BigDecimal(0);

    /**
     * 是否现场数据
     */
    @Excel(name = "是否现场", replace = {"是_true", "否_false"}, isMandatory = true, orderNum = "1200", width = 22, addressList = true)
    private Boolean isCompleteField;

    /**
     * 是否分包
     */
    @Excel(name = "分包类型", replace = {"不分包_-1", "分析分包_1","采测分包_2"}, orderNum = "1300", addressList = true)
    private Integer subcontractType;

    /**
     * 是否做质控平行
     */
    @Excel(name = "是否平行", replace = {"是_true", "否_false"}, orderNum = "1400", addressList = true)
    private Boolean isPx;

    /**
     * 是否做质控空白
     */
    @Excel(name = "是否空白", replace = {"是_true", "否_false"}, orderNum = "1500", addressList = true)
    private Boolean isKb;

    /**
     * 是否做串联样
     */
    @Excel(name = "是否串联", replace = {"是_true", "否_false"}, orderNum = "1600", addressList = true)
    private Boolean isCl;

    /**
     * 是否填写仪器使用记录（预留）
     */
    @Excel(name = "是否填写仪器", replace = {"是_true", "否_false"}, orderNum = "1700", width = 22, addressList = true)
    private Boolean isFillInstrumentUseLog;

    /**
     * 默认分析人员
     */
    @Excel(name = "默认分析人员", orderNum = "1800", width = 22)
    private String analyzePerson;

    /**
     * 默认分析人员id
     */
    private String analyzePersonId;

    /**
     * 测试项目名称
     */
    private String testName;


    @Override
    public Set<String> getUniqueFieldNames() {
        return Stream.of("analyzeMethodId##analyzeItemId##sampleTypeId").collect(Collectors.toSet());
    }
}
