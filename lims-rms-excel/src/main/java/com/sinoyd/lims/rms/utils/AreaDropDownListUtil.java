package com.sinoyd.lims.rms.utils;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.excel.utils.ExcelDropdownUtil;
import com.sinoyd.excel.vo.CascadingDropdownVO;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域级联下拉框生成工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/11/03
 */
public class AreaDropDownListUtil {

    /**
     * 创建区域省、市、县区级联下拉框
     *
     * @param workbook         工作簿
     * @param areaList         区域数据
     * @param provincialColIdx 省级区域列索引
     * @param municipalColIdx  市级区域列索引
     */
    public static void createAreaDropDownList(Workbook workbook, List<DtoArea> areaList,
                                              int provincialColIdx, int municipalColIdx, int countryColIdx) {
        //省级区域
        Map<String, String> provincialAreaMaps = areaList.stream()
                .filter(p -> UUIDHelper.guidEmpty().equals(p.getParentId()) || StringUtils.isNull(p.getParentId()) || "0".equals(p.getParentId()))
                .collect(Collectors.toMap(DtoArea::getId, DtoArea::getAreaName));
        //创建省、市级联下拉框
        createCascadeDropDownBoxForArea(workbook, provincialColIdx, municipalColIdx, "省区域", "省级与市级关联下拉框数据源", provincialAreaMaps, areaList);
        //市级区域
        Set<String> provincialAreaIds = provincialAreaMaps.keySet();
        Map<String, String> municipalAreaMaps = areaList.stream()
                .filter(p -> provincialAreaIds.contains(p.getParentId()))
                .collect(Collectors.toMap(DtoArea::getId, DtoArea::getAreaName));
        //创建市、县级联下拉框
        createCascadeDropDownBoxForArea(workbook, municipalColIdx, countryColIdx, "市区域", "市级与县级关联下拉框数据源", municipalAreaMaps, areaList);
    }

    /**
     * 创建区域级联下拉框
     *
     * @param workbook            需要操作的工作簿
     * @param startDropDownColIdx 下拉框开始列
     * @param endDropDownColIdx   下拉框结束列
     * @param firstMapKey         主区域MapKey值
     * @param dataSourceSheetName 数据源Sheet名称
     * @param areaMap             级联区域数据
     * @param areaList            区域数据
     */
    public static void createCascadeDropDownBoxForArea(Workbook workbook, int startDropDownColIdx, int endDropDownColIdx,
                                                       String firstMapKey, String dataSourceSheetName, Map<String, String> areaMap,
                                                       List<DtoArea> areaList) {
        //创建市级与县级级区域关联下拉框
        CascadingDropdownVO cascadingDropdownVO = new CascadingDropdownVO();
        List<Integer> cascadingDropdownColumnIdxList = new ArrayList<>();
        cascadingDropdownColumnIdxList.add(startDropDownColIdx);
        cascadingDropdownColumnIdxList.add(endDropDownColIdx);
        LinkedHashMap<String, List<String>> relationMap = new LinkedHashMap<>();
        //放置主区域数据
        relationMap.put(firstMapKey, new ArrayList<>(areaMap.values()));
        //放置次区域数据
        for (String areaId : areaMap.keySet()) {
            //市级区域名称集合
            List<String> countyAreaNameList = areaList.stream().filter(p -> areaId.equals(p.getParentId())).map(DtoArea::getAreaName).collect(Collectors.toList());
            relationMap.put(areaMap.get(areaId), countyAreaNameList);
        }
        cascadingDropdownVO.setDropdownDataList(relationMap)
                .setColumnIdxList(cascadingDropdownColumnIdxList)
                .setStartRowIdx(1)
                .setDropdownDataSheetName(dataSourceSheetName);
        ExcelDropdownUtil.createCascadeDropDownBox(workbook.getSheetAt(0), cascadingDropdownVO);
    }

}
