package com.sinoyd.lims.rms.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.service.impl.LimsBaseExcelServiceImpl;
import com.sinoyd.base.util.BeanUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.excel.annotations.GlobalExport;
import com.sinoyd.excel.builder.ImportParamsBuilder;
import com.sinoyd.excel.utils.ExcelUtil;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.service.AreaService;
import com.sinoyd.lims.rms.RmsSupplierService;
import com.sinoyd.lims.rms.constant.IImportConstants;
import com.sinoyd.lims.rms.criteria.RmsSupplierCriteria;
import com.sinoyd.lims.rms.dto.DtoRmsSupplier;
import com.sinoyd.lims.rms.handler.verify.ImportSupplierVerifyHandler;
import com.sinoyd.lims.rms.utils.AreaDropDownListUtil;
import com.sinoyd.lims.rms.utils.EnterPriseImportUtil;
import com.sinoyd.lims.rms.vo.ExcelSupplierVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商数据导入实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/17
 */
@Service
public class ExcelSupplierServiceImpl extends LimsBaseExcelServiceImpl<DtoRmsSupplier, ExcelSupplierVO, String, RmsSupplierService> {

    private AreaService areaService;

    /**
     * 分包商导入
     *
     * @param response 响应流
     * @param file     导入文件
     */
    @Override
    public void importData(HttpServletResponse response, MultipartFile file) {
        ImportSupplierVerifyHandler verifyHandler = new ImportSupplierVerifyHandler();
        ImportParamsBuilder importParamsBuilder = ImportParamsBuilder.create()
                .pojoClass(ExcelSupplierVO.class)
                .verifyHandler(verifyHandler);
        ExcelImportResult<ExcelSupplierVO> excelImportResult = pojoImportService.importData(importParamsBuilder.getImportParams(), file, response);
        boolean verifyResult = StringUtils.isEmpty(excelImportResult.getFailList()) && StringUtils.isNotEmpty(excelImportResult.getList());
        if (verifyResult) {
            List<DtoRmsSupplier> importList = turnToEntity(excelImportResult.getList());
            //循环处理关联Id以及初始值
            importList.forEach(EnterPriseImportUtil::handleImportData);
            //处理重复数据(重复数据做更新操作)
            EnterPriseImportUtil.handleRepeatData(importList, findAll());
            this.saveImportData(importList);
        }
    }

    /**
     * 下载导入模板
     *
     * @param response 响应体
     */
    @Override
    public void downloadImportTemplate(HttpServletResponse response) {
        Workbook workbook = pojoImportService.generateTemplateWorkbook(ExcelSupplierVO.class, null, null, response);
        //创建区域级联下拉框
        AreaDropDownListUtil.createAreaDropDownList(workbook, areaService.findAreasByCenterConfig(), 2, 3, 4);
        GlobalExport exportName = ExcelSupplierVO.class.getAnnotation(GlobalExport.class);
        if (exportName == null) {
            throw new RuntimeException("尚未配置模板名称");
        }
        String fileName = exportName.name();
        ExcelUtil.downLoadExcel(response, fileName, workbook);
    }

    /**
     * 保存数据
     *
     * @param importList 导入数据集合
     */
    @Transactional
    public void saveImportData(List<DtoRmsSupplier> importList) {
        service.save(importList);
    }

    /**
     * 导出数据
     *
     * @param response 响应流
     * @param criteria 查询条件
     */
    @Override
    public void exportData(HttpServletResponse response, BaseCriteria criteria) {
        List<DtoRmsSupplier> dataList = findExportData(criteria);
        List<ExcelSupplierVO> voList = new ArrayList<>();
        for (DtoRmsSupplier enterprise : dataList) {
            ExcelSupplierVO enterpriseExcelVo = BeanUtil.convertT2V(ExcelSupplierVO.class, enterprise);
            voList.add(enterpriseExcelVo);
        }
        handleExportArea(areaService.findAreasByCenterConfig(), voList);
        super.exportData(response, voList);
    }

    /**
     * 处理导入数据的省市县
     *
     * @param areaList 区域集合
     * @param voList   导出数据集合
     */
    private void handleExportArea(List<DtoArea> areaList, List<ExcelSupplierVO> voList) {
        //省级区域Map
        Map<String, DtoArea> areaMap = areaList.stream().collect(Collectors.toMap(DtoArea::getId, area -> area));
        //处理省市县区域名称
        for (ExcelSupplierVO exportVo : voList) {
            //清空区域名称
            exportVo.setAreaName(IImportConstants.EMPTY_STRING);
            //获取区域id
            String areaId = exportVo.getAreaId();
            //省级区域名称
            Map<String, String> areaNameMap = new HashMap<>();
            if (areaMap.containsKey(areaId)){
                DtoArea area = areaMap.get(areaId);
                areaNameMap = EnterPriseImportUtil.findExportAreaNames(areaList, area);
            }
            exportVo.setProvinceAreaName(areaNameMap.get(IImportConstants.AreaMapKey.PROVINCE_NAME));
            exportVo.setCityAreaName(areaNameMap.get(IImportConstants.AreaMapKey.CITY_NAME));
            exportVo.setAreaName(areaNameMap.get(IImportConstants.AreaMapKey.AREA_NAME));
        }
    }

    /**
     * 转换导入数据（供应商数据转换）
     *
     * @param importList 导入数据集合
     * @return 实体数据集合
     */
    protected List<DtoRmsSupplier> turnToEntity(List<ExcelSupplierVO> importList) {
        List<DtoRmsSupplier> supplierList = new ArrayList<>();
        List<DtoArea> areaList = areaService.findAreasByCenterConfig();
        for (ExcelSupplierVO excelVo : importList) {
            //供应商数据
            DtoRmsSupplier supplier = BeanUtil.convertV2T(DtoRmsSupplier.class, excelVo);
            //获取区域Id
            String areaId = EnterPriseImportUtil.handleAreaId(
                    areaList, excelVo.getProvinceAreaName(),
                    excelVo.getCityAreaName(), excelVo.getAreaName());
            supplier.setAreaId(areaId);
            supplierList.add(supplier);
        }
        return supplierList;
    }

    /**
     * 获取数据库数据（过滤假删）
     *
     * @return 所有不包含假删数据
     */
    protected List<DtoRmsSupplier> findAll() {
        return findExportData(new RmsSupplierCriteria());
    }

    @Autowired
    public void setAreaService(AreaService areaService) {
        this.areaService = areaService;
    }
}
