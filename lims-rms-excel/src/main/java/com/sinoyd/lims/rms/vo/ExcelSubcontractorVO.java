package com.sinoyd.lims.rms.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.sinoyd.base.vo.LimsExcelBaseVO;
import com.sinoyd.excel.annotations.GlobalExport;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分包商导入/导出操作传输类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@GlobalExport(name = "分包商一览表")
public class ExcelSubcontractorVO extends LimsExcelBaseVO {

    /**
     * 分包商名称
     */
    @Excel(name = "分包商名称", isMandatory = true, width = 20, orderNum = "100")
    private String name;
    /**
     * 所属区域(省)
     */
    @Excel(name = "所属区域(省)",orderNum = "300",width = 17)
    private String provinceAreaName;

    /**
     * 省区域id
     */
    private String provinceId;

    /**
     * 所属区域(市)
     */
    @Excel(name = "所属区域(市)",orderNum = "400",width = 17)
    private String cityAreaName;

    /**
     * 市区域id
     */
    private String cityId;

    /**
     * 所属区域(县、区)
     */
    @Excel(name = "所属区域(县、区)",orderNum = "500",width = 17)
    private String areaName;

    /**
     * 区县id
     */
    private String areaId;

    /**
     * 企业地址
     */
    @Excel(name = "企业地址", width = 20, orderNum = "600")
    private String address;

    /**
     * 法人代表
     */
    @Excel(name = "法人代表", width = 20, orderNum = "700")
    private String legalPerson;

    /**
     * 社会信用代码
     */
    @Excel(name = "社会信用代码", width = 20, orderNum = "800")
    private String usciCode;

    /**
     * 联系人
     */
    @Excel(name = "联系人", width = 20, orderNum = "900")
    private String contacts;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式", width = 20, orderNum = "1000")
    private String contactInfo;

    /**
     * 联系人传真
     */
    @Excel(name = "联系人传真", width = 20, orderNum = "1100")
    private String faxNo;

    /**
     * 联系邮箱
     */
    @Excel(name = "联系邮箱", width = 20, orderNum = "1200")
    private String email;

    /**
     * 简介
     */
    @Excel(name = "公司简介", width = 20, orderNum = "1300")
    private String companyProfile;

    /**
     * 是否合格
     */
    @Excel(name = "是否合格", replace = {"是_true","否_false"}, addressList = true, width = 15, orderNum = "110")
    private Boolean isQualified;

    /**
     * 设置唯一性字段名称，如果是组合字段唯一性，多个字段之间用##拼接，比如 name##age的形式
     */
    @Override
    public Set<String> getUniqueFieldNames() {
        return Stream.of("name##usciCode").collect(Collectors.toSet());
    }
}
