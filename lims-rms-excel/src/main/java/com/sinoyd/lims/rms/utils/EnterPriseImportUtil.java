package com.sinoyd.lims.rms.utils;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoArea;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.constant.IImportConstants;
import com.sinoyd.lims.rms.entity.GenericEnterprise;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业通用数据导入工具类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/17
 */
@Slf4j
public class EnterPriseImportUtil {

    /**
     * 根据省市区域获取区域Id
     *
     * @param provinceName 省级区域名称
     * @param cityName     市级区域名称
     * @param areaName     县级区域名称
     * @return 区域id
     */
    public static String handleAreaId(List<DtoArea> areaList, String provinceName, String cityName, String areaName) {
        String resultId = UUIDHelper.guidEmpty();
        if (StringUtils.isNotEmpty(provinceName)) {
            Optional<DtoArea> areaOp = areaList.stream().filter(p -> provinceName.equals(p.getAreaName())).findFirst();
            if (areaOp.isPresent()) {
                resultId = areaOp.get().getId();
            }
        }
        if (StringUtils.isNotEmpty(cityName)) {
            Optional<DtoArea> areaOp = areaList.stream().filter(p -> cityName.equals(p.getAreaName())).findFirst();
            if (areaOp.isPresent()) {
                resultId = areaOp.get().getId();
            }
        }
        if (StringUtils.isNotEmpty(areaName)) {
            Optional<DtoArea> areaOp = areaList.stream().filter(p -> areaName.equals(p.getAreaName())).findFirst();
            if (areaOp.isPresent()) {
                resultId = areaOp.get().getId();
            }
        }
        return resultId;
    }

    /**
     * 设置导出的区域名称
     *
     * @param areaList 区域数据集合
     * @param area     当前导出的区域数据
     */
    public static Map<String, String> findExportAreaNames(List<DtoArea> areaList, DtoArea area) {
        Map<String, String> areaNameMap = new HashMap<>();
        String provinceName = IImportConstants.EMPTY_STRING, cityName = IImportConstants.EMPTY_STRING, areaName = IImportConstants.EMPTY_STRING;
        if (StringUtils.isNotNull(area.getProvinceCode())) {
            Optional<DtoArea> provinceAreaOp = areaList.stream().filter(p -> area.getProvinceCode().equals(p.getAreaCode())).findFirst();
            if (provinceAreaOp.isPresent()) {
                provinceName = provinceAreaOp.get().getAreaName();
            }
        }
        if (StringUtils.isNotNull(area.getCityCode())) {
            Optional<DtoArea> cityAreaOp = areaList.stream().filter(p -> area.getCityCode().equals(p.getAreaCode())).findFirst();
            if (cityAreaOp.isPresent()) {
                cityName = cityAreaOp.get().getAreaName();
            }
        }
        //都为空说明为省级区域
        if (StringUtils.isNull(area.getProvinceCode())
                && StringUtils.isNull(area.getCityCode())
                && StringUtils.isNull(area.getCountryCode())) {
            provinceName = area.getAreaName();
        } else if (StringUtils.isNull(area.getCityCode())
                && StringUtils.isNull(area.getCountryCode())) {
            cityName = area.getAreaName();
        } else if (StringUtils.isNull(area.getCountryCode())) {
            areaName = area.getAreaName();
        }
        areaNameMap.put(IImportConstants.AreaMapKey.PROVINCE_NAME, provinceName);
        areaNameMap.put(IImportConstants.AreaMapKey.CITY_NAME, cityName);
        areaNameMap.put(IImportConstants.AreaMapKey.AREA_NAME, areaName);
        return areaNameMap;
    }

    /**
     * 处理导入数据
     *
     * @param enterprise 企业数据
     */
    public static <T extends GenericEnterprise> void handleImportData(T enterprise) {
        if (enterprise != null) {
            //占地面积
            Integer defaultInt = 0;
            //排序值
            enterprise.setOrderNum(defaultInt);
        }

    }

    /**
     * 处理重复数据(更新重复数据)
     *
     * @param enterpriseList 企业数据
     */
    public static <T extends GenericEnterprise> void handleRepeatData(List<T> enterpriseList, List<T> dbEnterprises) {
        for (T enterprise : enterpriseList) {
            //获取到重复的数据
            Optional<T> existsData = dbEnterprises.stream()
                    .filter(p -> enterprise.getName().equals(p.getName()))
                    .findFirst();
            //做更新操作
            existsData.ifPresent(p -> enterprise.setId(p.getId()));
        }
    }
}
