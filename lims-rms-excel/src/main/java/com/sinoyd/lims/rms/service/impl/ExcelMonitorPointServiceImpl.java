package com.sinoyd.lims.rms.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.base.service.impl.LimsBaseExcelServiceImpl;
import com.sinoyd.base.util.BeanUtil;
import com.sinoyd.excel.builder.ImportParamsBuilder;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.lims.rms.dto.DtoMonitorStation;
import com.sinoyd.lims.rms.repository.MonitorStationRepository;
import com.sinoyd.lims.rms.RmsMonitorPlanPointService;
import com.sinoyd.lims.rms.criteria.RmsMonitorPlanPointCriteria;
import com.sinoyd.lims.rms.dto.DtoRmsMonitorPlanPoint;
import com.sinoyd.lims.rms.handler.verify.ImportMonitorPointVerifyHandler;
import com.sinoyd.lims.rms.vo.ExcelMonitorPointVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 环境质量点位数据导入实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2023/07/25
 */
@Service
public class ExcelMonitorPointServiceImpl
        extends LimsBaseExcelServiceImpl<DtoRmsMonitorPlanPoint, ExcelMonitorPointVO, String, RmsMonitorPlanPointService> {

    private MonitorStationRepository monitorStationRepository;

    @Override
    public void importData(HttpServletResponse response, MultipartFile file) {
        List<DtoMonitorStation> stationList = monitorStationRepository.findByIsDeletedFalse();
        ImportMonitorPointVerifyHandler verifyHandler = new ImportMonitorPointVerifyHandler(
                findExportData(new RmsMonitorPlanPointCriteria()),
                stationList);
        ImportParamsBuilder importParamsBuilder = ImportParamsBuilder.create().verifyHandler(verifyHandler)
                .pojoClass(ExcelMonitorPointVO.class);
        ExcelImportResult<ExcelMonitorPointVO> excelImportResult = pojoImportService.importData(importParamsBuilder.getImportParams(), file, response);
        if (!excelImportResult.isVerifyFail()) {
            List<DtoRmsMonitorPlanPoint> monitorPoints = turnToEntity(excelImportResult.getList(),stationList);
            service.save(monitorPoints);
        }
    }

    /**
     * 将DTO转成VO，用于导出
     *
     * @param dtoCollection DTO集合
     * @return VO集合
     */
    @Override
    protected List<ExcelMonitorPointVO> convertT2V(Collection<DtoRmsMonitorPlanPoint> dtoCollection) {
        List<DtoMonitorStation> stationList = monitorStationRepository.findByIsDeletedFalse();
        Map<String, String> stationNameMap = stationList.stream().collect(Collectors.toMap(DtoMonitorStation::getId,DtoMonitorStation::getName));
        List<ExcelMonitorPointVO> exportData = new ArrayList<>();
        for (DtoRmsMonitorPlanPoint rmsMonitorPoint : dtoCollection) {
            ExcelMonitorPointVO excelVo = super.convertT2V(rmsMonitorPoint);
            excelVo.setStation(stationNameMap.getOrDefault(rmsMonitorPoint.getStationId(), ""));
            exportData.add(excelVo);
        }
        return exportData;
    }

    /**
     * 转换实体数据
     *
     * @param excelVoList 导入数据
     * @return 实体数据
     */
    private List<DtoRmsMonitorPlanPoint> turnToEntity(List<ExcelMonitorPointVO> excelVoList,List<DtoMonitorStation> stationList) {
        List<DtoRmsMonitorPlanPoint> resultList = new ArrayList<>();
        for (ExcelMonitorPointVO excelVo : excelVoList) {
            Optional<DtoMonitorStation> station = stationList.stream().filter(p -> excelVo.getStation().equals(p.getName())).findFirst();
            DtoRmsMonitorPlanPoint monitorPoint = BeanUtil.convertV2T(DtoRmsMonitorPlanPoint.class, excelVo);
            String stationId = UUIDHelper.guidEmpty();
            if (station.isPresent()){
                stationId = station.get().getId();
            }
            monitorPoint.setStationId(stationId);
            resultList.add(monitorPoint);
        }
        return resultList;
    }

    @Autowired
    public void setMonitorStationRepository(MonitorStationRepository monitorStationRepository) {
        this.monitorStationRepository = monitorStationRepository;
    }
}
